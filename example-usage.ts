/**
 * ElectronClient 事件功能使用示例
 */

import { electronClient } from './src/renderer/src/utils/electronClient'

// ============================================================================
// 示例 1: 基本的本地事件使用
// ============================================================================

function basicLocalEventsExample() {
  console.log('=== 基本本地事件示例 ===')
  
  // 监听自定义事件
  electronClient.addEventListener('app-state-change', (newState) => {
    console.log('应用状态变化:', newState)
  })
  
  // 发送事件
  electronClient.emitLocal('app-state-change', { state: 'loading', progress: 0 })
  electronClient.emitLocal('app-state-change', { state: 'ready', progress: 100 })
}

// ============================================================================
// 示例 2: 下载任务事件处理
// ============================================================================

function downloadTaskExample() {
  console.log('=== 下载任务事件示例 ===')
  
  const taskId = 'download-task-001'
  
  // 监听下载进度
  electronClient.addEventListener(`progress-${taskId}`, (progress) => {
    console.log(`下载进度 [${taskId}]:`, progress)
    updateProgressBar(progress.percent)
  })
  
  // 监听下载完成
  electronClient.addEventListener(`complete-${taskId}`, () => {
    console.log(`下载完成 [${taskId}]`)
    showSuccessMessage('下载完成!')
  })
  
  // 监听下载错误
  electronClient.addEventListener(`error-${taskId}`, (error) => {
    console.error(`下载错误 [${taskId}]:`, error)
    showErrorMessage('下载失败: ' + error.message)
  })
  
  // 模拟下载过程
  simulateDownload(taskId)
}

function simulateDownload(taskId: string) {
  let progress = 0
  const interval = setInterval(() => {
    progress += 10
    electronClient.emitLocal(`progress-${taskId}`, { 
      percent: progress, 
      speed: '2MB/s',
      remaining: Math.max(0, 100 - progress) + 's'
    })
    
    if (progress >= 100) {
      clearInterval(interval)
      electronClient.emitLocal(`complete-${taskId}`)
    }
  }, 200)
}

// ============================================================================
// 示例 3: 主进程事件处理（兼容性）
// ============================================================================

function mainProcessEventsExample() {
  console.log('=== 主进程事件示例 ===')
  
  // 使用新的方法监听主进程事件
  const unsubscribeTheme = electronClient.onMainProcess('system-theme-changed', (theme) => {
    console.log('系统主题变化:', theme)
    applyTheme(theme)
  })
  
  // 使用便捷方法
  const unsubscribeNotification = electronClient.onNotification((notification) => {
    console.log('收到通知:', notification)
    showNotification(notification)
  })
  
  // 发送用户操作事件到主进程
  electronClient.emitUserAction('button-click', { buttonId: 'download-btn' })
  
  // 清理监听器
  setTimeout(() => {
    unsubscribeTheme()
    unsubscribeNotification()
    console.log('已清理主进程事件监听器')
  }, 5000)
}

// ============================================================================
// 示例 4: 事件链和转发
// ============================================================================

function eventChainExample() {
  console.log('=== 事件链示例 ===')
  
  // 监听主进程的下载事件，转发为本地事件
  electronClient.onMainProcess('download-event', (downloadEvent) => {
    const localEventName = `local-${downloadEvent.event}-${downloadEvent.taskId}`
    electronClient.emitLocal(localEventName, downloadEvent.data)
  })
  
  // 监听转发的本地事件
  electronClient.addEventListener('local-progress-task123', (data) => {
    console.log('本地下载进度事件:', data)
  })
  
  electronClient.addEventListener('local-complete-task123', (data) => {
    console.log('本地下载完成事件:', data)
  })
}

// ============================================================================
// 示例 5: 事件管理和清理
// ============================================================================

function eventManagementExample() {
  console.log('=== 事件管理示例 ===')
  
  const eventName = 'user-interaction'
  
  // 添加多个监听器
  const listener1 = (data: any) => console.log('监听器1:', data)
  const listener2 = (data: any) => console.log('监听器2:', data)
  const listener3 = (data: any) => console.log('监听器3:', data)
  
  electronClient.addEventListener(eventName, listener1)
  electronClient.addEventListener(eventName, listener2)
  electronClient.addEventListener(eventName, listener3)
  
  console.log(`事件 ${eventName} 的监听器数量:`, electronClient.listenerCount(eventName))
  
  // 发送事件
  electronClient.emitLocal(eventName, { action: 'click', target: 'button' })
  
  // 移除特定监听器
  electronClient.removeEventListener(eventName, listener2)
  console.log(`移除一个监听器后的数量:`, electronClient.listenerCount(eventName))
  
  // 再次发送事件
  electronClient.emitLocal(eventName, { action: 'hover', target: 'link' })
  
  // 清理所有事件
  setTimeout(() => {
    electronClient.cleanup()
    console.log('已清理所有事件监听器')
    console.log(`清理后的监听器数量:`, electronClient.listenerCount(eventName))
  }, 2000)
}

// ============================================================================
// 辅助函数
// ============================================================================

function updateProgressBar(percent: number) {
  console.log(`进度条更新: ${percent}%`)
}

function showSuccessMessage(message: string) {
  console.log(`✅ ${message}`)
}

function showErrorMessage(message: string) {
  console.log(`❌ ${message}`)
}

function applyTheme(theme: 'light' | 'dark') {
  console.log(`应用主题: ${theme}`)
  document.body.className = `theme-${theme}`
}

function showNotification(notification: any) {
  console.log(`🔔 通知: ${notification.title} - ${notification.body}`)
}

// ============================================================================
// 运行所有示例
// ============================================================================

export function runAllExamples() {
  console.log('开始运行 ElectronClient 事件功能示例...\n')
  
  basicLocalEventsExample()
  setTimeout(() => downloadTaskExample(), 500)
  setTimeout(() => mainProcessEventsExample(), 1000)
  setTimeout(() => eventChainExample(), 1500)
  setTimeout(() => eventManagementExample(), 2000)
  
  setTimeout(() => {
    console.log('\n所有示例运行完成!')
  }, 8000)
}

// 如果在浏览器环境中运行
if (typeof window !== 'undefined') {
  // 等待 DOM 加载完成
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', runAllExamples)
  } else {
    runAllExamples()
  }
}

export {
  basicLocalEventsExample,
  downloadTaskExample,
  mainProcessEventsExample,
  eventChainExample,
  eventManagementExample
}
