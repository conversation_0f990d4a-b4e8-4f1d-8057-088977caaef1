const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/AboutView-HcdbvEAf.js","assets/AboutView-E68f7lAM.css"])))=>i.map(i=>d[i]);
(function(){let e=document.createElement(`link`).relList;if(e&&e.supports&&e.supports(`modulepreload`))return;for(let e of document.querySelectorAll(`link[rel="modulepreload"]`))n(e);new MutationObserver(e=>{for(let t of e){if(t.type!==`childList`)continue;for(let e of t.addedNodes)e.tagName===`LINK`&&e.rel===`modulepreload`&&n(e)}}).observe(document,{childList:!0,subtree:!0});function t(e){let t={};return e.integrity&&(t.integrity=e.integrity),e.referrerPolicy&&(t.referrerPolicy=e.referrerPolicy),e.crossOrigin===`use-credentials`?t.credentials=`include`:e.crossOrigin===`anonymous`?t.credentials=`omit`:t.credentials=`same-origin`,t}function n(e){if(e.ep)return;e.ep=!0;let n=t(e);fetch(e.href,n)}})();
/**
* @vue/shared v3.5.18
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/
/*! #__NO_SIDE_EFFECTS__ */
function e(e){let t=Object.create(null);for(let n of e.split(`,`))t[n]=1;return e=>e in t}const t={},n=[],r=()=>{},i=()=>!1,a=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),o=e=>e.startsWith(`onUpdate:`),s=Object.assign,c=(e,t)=>{let n=e.indexOf(t);n>-1&&e.splice(n,1)},l=Object.prototype.hasOwnProperty,u=(e,t)=>l.call(e,t),d=Array.isArray,f=e=>b(e)===`[object Map]`,p=e=>b(e)===`[object Set]`,m=e=>typeof e==`function`,h=e=>typeof e==`string`,g=e=>typeof e==`symbol`,_=e=>typeof e==`object`&&!!e,v=e=>(_(e)||m(e))&&m(e.then)&&m(e.catch),y=Object.prototype.toString,b=e=>y.call(e),x=e=>b(e).slice(8,-1),S=e=>b(e)===`[object Object]`,C=e=>h(e)&&e!==`NaN`&&e[0]!==`-`&&``+parseInt(e,10)===e,w=e(`,key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted`),ee=e=>{let t=Object.create(null);return n=>{let r=t[n];return r||(t[n]=e(n))}},te=/-(\w)/g,T=ee(e=>e.replace(te,(e,t)=>t?t.toUpperCase():``)),ne=/\B([A-Z])/g,E=ee(e=>e.replace(ne,`-$1`).toLowerCase()),re=ee(e=>e.charAt(0).toUpperCase()+e.slice(1)),D=ee(e=>{let t=e?`on${re(e)}`:``;return t}),O=(e,t)=>!Object.is(e,t),ie=(e,...t)=>{for(let n=0;n<e.length;n++)e[n](...t)},ae=(e,t,n,r=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:r,value:n})},oe=e=>{let t=parseFloat(e);return isNaN(t)?e:t};let k;const se=()=>k||=typeof globalThis<`u`?globalThis:typeof self<`u`?self:typeof window<`u`?window:typeof global<`u`?global:{};function ce(e){if(d(e)){let t={};for(let n=0;n<e.length;n++){let r=e[n],i=h(r)?fe(r):ce(r);if(i)for(let e in i)t[e]=i[e]}return t}else if(h(e)||_(e))return e}const le=/;(?![^(]*\))/g,ue=/:([^]+)/,de=/\/\*[^]*?\*\//g;function fe(e){let t={};return e.replace(de,``).split(le).forEach(e=>{if(e){let n=e.split(ue);n.length>1&&(t[n[0].trim()]=n[1].trim())}}),t}function pe(e){let t=``;if(h(e))t=e;else if(d(e))for(let n=0;n<e.length;n++){let r=pe(e[n]);r&&(t+=r+` `)}else if(_(e))for(let n in e)e[n]&&(t+=n+` `);return t.trim()}const me=`itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly`,he=e(me);me+``;function ge(e){return!!e||e===``}const _e=e=>!!(e&&e.__v_isRef===!0),ve=e=>h(e)?e:e==null?``:d(e)||_(e)&&(e.toString===y||!m(e.toString))?_e(e)?ve(e.value):JSON.stringify(e,ye,2):String(e),ye=(e,t)=>_e(t)?ye(e,t.value):f(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((e,[t,n],r)=>(e[be(t,r)+` =>`]=n,e),{})}:p(t)?{[`Set(${t.size})`]:[...t.values()].map(e=>be(e))}:g(t)?be(t):_(t)&&!d(t)&&!S(t)?String(t):t,be=(e,t=``)=>{var n;return g(e)?`Symbol(${(n=e.description)??t})`:e};let A;var xe=class{constructor(e=!1){this.detached=e,this._active=!0,this._on=0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=A,!e&&A&&(this.index=(A.scopes||=[]).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let e,t;if(this.scopes)for(e=0,t=this.scopes.length;e<t;e++)this.scopes[e].pause();for(e=0,t=this.effects.length;e<t;e++)this.effects[e].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let e,t;if(this.scopes)for(e=0,t=this.scopes.length;e<t;e++)this.scopes[e].resume();for(e=0,t=this.effects.length;e<t;e++)this.effects[e].resume()}}run(e){if(this._active){let t=A;try{return A=this,e()}finally{A=t}}}on(){++this._on===1&&(this.prevScope=A,A=this)}off(){this._on>0&&--this._on===0&&(A=this.prevScope,this.prevScope=void 0)}stop(e){if(this._active){this._active=!1;let t,n;for(t=0,n=this.effects.length;t<n;t++)this.effects[t].stop();for(this.effects.length=0,t=0,n=this.cleanups.length;t<n;t++)this.cleanups[t]();if(this.cleanups.length=0,this.scopes){for(t=0,n=this.scopes.length;t<n;t++)this.scopes[t].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!e){let e=this.parent.scopes.pop();e&&e!==this&&(this.parent.scopes[this.index]=e,e.index=this.index)}this.parent=void 0}}};function Se(e){return new xe(e)}function Ce(){return A}let j;const we=new WeakSet;var Te=class{constructor(e){this.fn=e,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,A&&A.active&&A.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,we.has(this)&&(we.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||ke(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,Ve(this),Me(this);let e=j,t=M;j=this,M=!0;try{return this.fn()}finally{Ne(this),j=e,M=t,this.flags&=-3}}stop(){if(this.flags&1){for(let e=this.deps;e;e=e.nextDep)Ie(e);this.deps=this.depsTail=void 0,Ve(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?we.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){Pe(this)&&this.run()}get dirty(){return Pe(this)}};let Ee=0,De,Oe;function ke(e,t=!1){if(e.flags|=8,t){e.next=Oe,Oe=e;return}e.next=De,De=e}function Ae(){Ee++}function je(){if(--Ee>0)return;if(Oe){let e=Oe;for(Oe=void 0;e;){let t=e.next;e.next=void 0,e.flags&=-9,e=t}}let e;for(;De;){let t=De;for(De=void 0;t;){let n=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(t){e||=t}t=n}}if(e)throw e}function Me(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function Ne(e){let t,n=e.depsTail,r=n;for(;r;){let e=r.prevDep;r.version===-1?(r===n&&(n=e),Ie(r),Le(r)):t=r,r.dep.activeLink=r.prevActiveLink,r.prevActiveLink=void 0,r=e}e.deps=t,e.depsTail=n}function Pe(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(Fe(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function Fe(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===He)||(e.globalVersion=He,!e.isSSR&&e.flags&128&&(!e.deps&&!e._dirty||!Pe(e))))return;e.flags|=2;let t=e.dep,n=j,r=M;j=e,M=!0;try{Me(e);let n=e.fn(e._value);(t.version===0||O(n,e._value))&&(e.flags|=128,e._value=n,t.version++)}catch(e){throw t.version++,e}finally{j=n,M=r,Ne(e),e.flags&=-3}}function Ie(e,t=!1){let{dep:n,prevSub:r,nextSub:i}=e;if(r&&(r.nextSub=i,e.prevSub=void 0),i&&(i.prevSub=r,e.nextSub=void 0),n.subs===e&&(n.subs=r,!r&&n.computed)){n.computed.flags&=-5;for(let e=n.computed.deps;e;e=e.nextDep)Ie(e,!0)}!t&&!--n.sc&&n.map&&n.map.delete(n.key)}function Le(e){let{prevDep:t,nextDep:n}=e;t&&(t.nextDep=n,e.prevDep=void 0),n&&(n.prevDep=t,e.nextDep=void 0)}let M=!0;const Re=[];function ze(){Re.push(M),M=!1}function Be(){let e=Re.pop();M=e===void 0?!0:e}function Ve(e){let{cleanup:t}=e;if(e.cleanup=void 0,t){let e=j;j=void 0;try{t()}finally{j=e}}}let He=0;var Ue=class{constructor(e,t){this.sub=e,this.dep=t,this.version=t.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}},We=class{constructor(e){this.computed=e,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0,this.__v_skip=!0}track(e){if(!j||!M||j===this.computed)return;let t=this.activeLink;if(t===void 0||t.sub!==j)t=this.activeLink=new Ue(j,this),j.deps?(t.prevDep=j.depsTail,j.depsTail.nextDep=t,j.depsTail=t):j.deps=j.depsTail=t,Ge(t);else if(t.version===-1&&(t.version=this.version,t.nextDep)){let e=t.nextDep;e.prevDep=t.prevDep,t.prevDep&&(t.prevDep.nextDep=e),t.prevDep=j.depsTail,t.nextDep=void 0,j.depsTail.nextDep=t,j.depsTail=t,j.deps===t&&(j.deps=e)}return t}trigger(e){this.version++,He++,this.notify(e)}notify(e){Ae();try{for(let e=this.subs;e;e=e.prevSub)e.sub.notify()&&e.sub.dep.notify()}finally{je()}}};function Ge(e){if(e.dep.sc++,e.sub.flags&4){let t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let e=t.deps;e;e=e.nextDep)Ge(e)}let n=e.dep.subs;n!==e&&(e.prevSub=n,n&&(n.nextSub=e)),e.dep.subs=e}}const Ke=new WeakMap,qe=Symbol(``),Je=Symbol(``),Ye=Symbol(``);function N(e,t,n){if(M&&j){let t=Ke.get(e);t||Ke.set(e,t=new Map);let r=t.get(n);r||(t.set(n,r=new We),r.map=t,r.key=n),r.track()}}function Xe(e,t,n,r,i,a){let o=Ke.get(e);if(!o){He++;return}let s=e=>{e&&e.trigger()};if(Ae(),t===`clear`)o.forEach(s);else{let i=d(e),a=i&&C(n);if(i&&n===`length`){let e=Number(r);o.forEach((t,n)=>{(n===`length`||n===Ye||!g(n)&&n>=e)&&s(t)})}else switch((n!==void 0||o.has(void 0))&&s(o.get(n)),a&&s(o.get(Ye)),t){case`add`:i?a&&s(o.get(`length`)):(s(o.get(qe)),f(e)&&s(o.get(Je)));break;case`delete`:i||(s(o.get(qe)),f(e)&&s(o.get(Je)));break;case`set`:f(e)&&s(o.get(qe));break}}je()}function Ze(e){let t=F(e);return t===e?t:(N(t,`iterate`,Ye),P(e)?t:t.map(I))}function Qe(e){return N(e=F(e),`iterate`,Ye),e}const $e={__proto__:null,[Symbol.iterator](){return et(this,Symbol.iterator,I)},concat(...e){return Ze(this).concat(...e.map(e=>d(e)?Ze(e):e))},entries(){return et(this,`entries`,e=>(e[1]=I(e[1]),e))},every(e,t){return nt(this,`every`,e,t,void 0,arguments)},filter(e,t){return nt(this,`filter`,e,t,e=>e.map(I),arguments)},find(e,t){return nt(this,`find`,e,t,I,arguments)},findIndex(e,t){return nt(this,`findIndex`,e,t,void 0,arguments)},findLast(e,t){return nt(this,`findLast`,e,t,I,arguments)},findLastIndex(e,t){return nt(this,`findLastIndex`,e,t,void 0,arguments)},forEach(e,t){return nt(this,`forEach`,e,t,void 0,arguments)},includes(...e){return it(this,`includes`,e)},indexOf(...e){return it(this,`indexOf`,e)},join(e){return Ze(this).join(e)},lastIndexOf(...e){return it(this,`lastIndexOf`,e)},map(e,t){return nt(this,`map`,e,t,void 0,arguments)},pop(){return at(this,`pop`)},push(...e){return at(this,`push`,e)},reduce(e,...t){return rt(this,`reduce`,e,t)},reduceRight(e,...t){return rt(this,`reduceRight`,e,t)},shift(){return at(this,`shift`)},some(e,t){return nt(this,`some`,e,t,void 0,arguments)},splice(...e){return at(this,`splice`,e)},toReversed(){return Ze(this).toReversed()},toSorted(e){return Ze(this).toSorted(e)},toSpliced(...e){return Ze(this).toSpliced(...e)},unshift(...e){return at(this,`unshift`,e)},values(){return et(this,`values`,I)}};function et(e,t,n){let r=Qe(e),i=r[t]();return r!==e&&!P(e)&&(i._next=i.next,i.next=()=>{let e=i._next();return e.value&&=n(e.value),e}),i}const tt=Array.prototype;function nt(e,t,n,r,i,a){let o=Qe(e),s=o!==e&&!P(e),c=o[t];if(c!==tt[t]){let t=c.apply(e,a);return s?I(t):t}let l=n;o!==e&&(s?l=function(t,r){return n.call(this,I(t),r,e)}:n.length>2&&(l=function(t,r){return n.call(this,t,r,e)}));let u=c.call(o,l,r);return s&&i?i(u):u}function rt(e,t,n,r){let i=Qe(e),a=n;return i!==e&&(P(e)?n.length>3&&(a=function(t,r,i){return n.call(this,t,r,i,e)}):a=function(t,r,i){return n.call(this,t,I(r),i,e)}),i[t](a,...r)}function it(e,t,n){let r=F(e);N(r,`iterate`,Ye);let i=r[t](...n);return(i===-1||i===!1)&&It(n[0])?(n[0]=F(n[0]),r[t](...n)):i}function at(e,t,n=[]){ze(),Ae();let r=F(e)[t].apply(e,n);return je(),Be(),r}const ot=e(`__proto__,__v_isRef,__isVue`),st=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!==`arguments`&&e!==`caller`).map(e=>Symbol[e]).filter(g));function ct(e){g(e)||(e=String(e));let t=F(this);return N(t,`has`,e),t.hasOwnProperty(e)}var lt=class{constructor(e=!1,t=!1){this._isReadonly=e,this._isShallow=t}get(e,t,n){if(t===`__v_skip`)return e.__v_skip;let r=this._isReadonly,i=this._isShallow;if(t===`__v_isReactive`)return!r;if(t===`__v_isReadonly`)return r;if(t===`__v_isShallow`)return i;if(t===`__v_raw`)return n===(r?i?Dt:Et:i?Tt:wt).get(e)||Object.getPrototypeOf(e)===Object.getPrototypeOf(n)?e:void 0;let a=d(e);if(!r){let e;if(a&&(e=$e[t]))return e;if(t===`hasOwnProperty`)return ct}let o=Reflect.get(e,t,L(e)?e:n);return(g(t)?st.has(t):ot(t))||(r||N(e,`get`,t),i)?o:L(o)?a&&C(t)?o:o.value:_(o)?r?Mt(o):At(o):o}},ut=class extends lt{constructor(e=!1){super(!1,e)}set(e,t,n,r){let i=e[t];if(!this._isShallow){let t=Ft(i);if(!P(n)&&!Ft(n)&&(i=F(i),n=F(n)),!d(e)&&L(i)&&!L(n))return t?!1:(i.value=n,!0)}let a=d(e)&&C(t)?Number(t)<e.length:u(e,t),o=Reflect.set(e,t,n,L(e)?e:r);return e===F(r)&&(a?O(n,i)&&Xe(e,`set`,t,n,i):Xe(e,`add`,t,n)),o}deleteProperty(e,t){let n=u(e,t),r=e[t],i=Reflect.deleteProperty(e,t);return i&&n&&Xe(e,`delete`,t,void 0,r),i}has(e,t){let n=Reflect.has(e,t);return(!g(t)||!st.has(t))&&N(e,`has`,t),n}ownKeys(e){return N(e,`iterate`,d(e)?`length`:qe),Reflect.ownKeys(e)}},dt=class extends lt{constructor(e=!1){super(!0,e)}set(e,t){return!0}deleteProperty(e,t){return!0}};const ft=new ut,pt=new dt,mt=new ut(!0),ht=e=>e,gt=e=>Reflect.getPrototypeOf(e);function _t(e,t,n){return function(...r){let i=this.__v_raw,a=F(i),o=f(a),s=e===`entries`||e===Symbol.iterator&&o,c=e===`keys`&&o,l=i[e](...r),u=n?ht:t?Rt:I;return!t&&N(a,`iterate`,c?Je:qe),{next(){let{value:e,done:t}=l.next();return t?{value:e,done:t}:{value:s?[u(e[0]),u(e[1])]:u(e),done:t}},[Symbol.iterator](){return this}}}}function vt(e){return function(...t){return e===`delete`?!1:e===`clear`?void 0:this}}function yt(e,t){let n={get(n){let r=this.__v_raw,i=F(r),a=F(n);e||(O(n,a)&&N(i,`get`,n),N(i,`get`,a));let{has:o}=gt(i),s=t?ht:e?Rt:I;if(o.call(i,n))return s(r.get(n));if(o.call(i,a))return s(r.get(a));r!==i&&r.get(n)},get size(){let t=this.__v_raw;return!e&&N(F(t),`iterate`,qe),Reflect.get(t,`size`,t)},has(t){let n=this.__v_raw,r=F(n),i=F(t);return e||(O(t,i)&&N(r,`has`,t),N(r,`has`,i)),t===i?n.has(t):n.has(t)||n.has(i)},forEach(n,r){let i=this,a=i.__v_raw,o=F(a),s=t?ht:e?Rt:I;return!e&&N(o,`iterate`,qe),a.forEach((e,t)=>n.call(r,s(e),s(t),i))}};s(n,e?{add:vt(`add`),set:vt(`set`),delete:vt(`delete`),clear:vt(`clear`)}:{add(e){!t&&!P(e)&&!Ft(e)&&(e=F(e));let n=F(this),r=gt(n),i=r.has.call(n,e);return i||(n.add(e),Xe(n,`add`,e,e)),this},set(e,n){!t&&!P(n)&&!Ft(n)&&(n=F(n));let r=F(this),{has:i,get:a}=gt(r),o=i.call(r,e);o||(e=F(e),o=i.call(r,e));let s=a.call(r,e);return r.set(e,n),o?O(n,s)&&Xe(r,`set`,e,n,s):Xe(r,`add`,e,n),this},delete(e){let t=F(this),{has:n,get:r}=gt(t),i=n.call(t,e);i||(e=F(e),i=n.call(t,e));let a=r?r.call(t,e):void 0,o=t.delete(e);return i&&Xe(t,`delete`,e,void 0,a),o},clear(){let e=F(this),t=e.size!==0,n,r=e.clear();return t&&Xe(e,`clear`,void 0,void 0,n),r}});let r=[`keys`,`values`,`entries`,Symbol.iterator];return r.forEach(r=>{n[r]=_t(r,e,t)}),n}function bt(e,t){let n=yt(e,t);return(t,r,i)=>r===`__v_isReactive`?!e:r===`__v_isReadonly`?e:r===`__v_raw`?t:Reflect.get(u(n,r)&&r in t?n:t,r,i)}const xt={get:bt(!1,!1)},St={get:bt(!1,!0)},Ct={get:bt(!0,!1)},wt=new WeakMap,Tt=new WeakMap,Et=new WeakMap,Dt=new WeakMap;function Ot(e){switch(e){case`Object`:case`Array`:return 1;case`Map`:case`Set`:case`WeakMap`:case`WeakSet`:return 2;default:return 0}}function kt(e){return e.__v_skip||!Object.isExtensible(e)?0:Ot(x(e))}function At(e){return Ft(e)?e:Nt(e,!1,ft,xt,wt)}function jt(e){return Nt(e,!1,mt,St,Tt)}function Mt(e){return Nt(e,!0,pt,Ct,Et)}function Nt(e,t,n,r,i){if(!_(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;let a=kt(e);if(a===0)return e;let o=i.get(e);if(o)return o;let s=new Proxy(e,a===2?r:n);return i.set(e,s),s}function Pt(e){return Ft(e)?Pt(e.__v_raw):!!(e&&e.__v_isReactive)}function Ft(e){return!!(e&&e.__v_isReadonly)}function P(e){return!!(e&&e.__v_isShallow)}function It(e){return e?!!e.__v_raw:!1}function F(e){let t=e&&e.__v_raw;return t?F(t):e}function Lt(e){return!u(e,`__v_skip`)&&Object.isExtensible(e)&&ae(e,`__v_skip`,!0),e}const I=e=>_(e)?At(e):e,Rt=e=>_(e)?Mt(e):e;function L(e){return e?e.__v_isRef===!0:!1}function zt(e){return Vt(e,!1)}function Bt(e){return Vt(e,!0)}function Vt(e,t){return L(e)?e:new Ht(e,t)}var Ht=class{constructor(e,t){this.dep=new We,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=t?e:F(e),this._value=t?e:I(e),this.__v_isShallow=t}get value(){return this.dep.track(),this._value}set value(e){let t=this._rawValue,n=this.__v_isShallow||P(e)||Ft(e);e=n?e:F(e),O(e,t)&&(this._rawValue=e,this._value=n?e:I(e),this.dep.trigger())}};function Ut(e){return L(e)?e.value:e}const Wt={get:(e,t,n)=>t===`__v_raw`?e:Ut(Reflect.get(e,t,n)),set:(e,t,n,r)=>{let i=e[t];return L(i)&&!L(n)?(i.value=n,!0):Reflect.set(e,t,n,r)}};function Gt(e){return Pt(e)?e:new Proxy(e,Wt)}var Kt=class{constructor(e,t,n){this.fn=e,this.setter=t,this._value=void 0,this.dep=new We(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=He-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!t,this.isSSR=n}notify(){if(this.flags|=16,!(this.flags&8)&&j!==this)return ke(this,!0),!0}get value(){let e=this.dep.track();return Fe(this),e&&(e.version=this.dep.version),this._value}set value(e){this.setter&&this.setter(e)}};function qt(e,t,n=!1){let r,i;m(e)?r=e:(r=e.get,i=e.set);let a=new Kt(r,i,n);return a}const Jt={},Yt=new WeakMap;let Xt;function Zt(e,t=!1,n=Xt){if(n){let t=Yt.get(n);t||Yt.set(n,t=[]),t.push(e)}}function Qt(e,n,i=t){let{immediate:a,deep:o,once:s,scheduler:l,augmentJob:u,call:f}=i,p=e=>o?e:P(e)||o===!1||o===0?$t(e,1):$t(e),h,g,_,v,y=!1,b=!1;if(L(e)?(g=()=>e.value,y=P(e)):Pt(e)?(g=()=>p(e),y=!0):d(e)?(b=!0,y=e.some(e=>Pt(e)||P(e)),g=()=>e.map(e=>{if(L(e))return e.value;if(Pt(e))return p(e);if(m(e))return f?f(e,2):e()})):g=m(e)?n?f?()=>f(e,2):e:()=>{if(_){ze();try{_()}finally{Be()}}let t=Xt;Xt=h;try{return f?f(e,3,[v]):e(v)}finally{Xt=t}}:r,n&&o){let e=g,t=o===!0?1/0:o;g=()=>$t(e(),t)}let x=Ce(),S=()=>{h.stop(),x&&x.active&&c(x.effects,h)};if(s&&n){let e=n;n=(...t)=>{e(...t),S()}}let C=b?Array(e.length).fill(Jt):Jt,w=e=>{if(!(!(h.flags&1)||!h.dirty&&!e))if(n){let e=h.run();if(o||y||(b?e.some((e,t)=>O(e,C[t])):O(e,C))){_&&_();let t=Xt;Xt=h;try{let t=[e,C===Jt?void 0:b&&C[0]===Jt?[]:C,v];C=e,f?f(n,3,t):n(...t)}finally{Xt=t}}}else h.run()};return u&&u(w),h=new Te(g),h.scheduler=l?()=>l(w,!1):w,v=e=>Zt(e,!1,h),_=h.onStop=()=>{let e=Yt.get(h);if(e){if(f)f(e,4);else for(let t of e)t();Yt.delete(h)}},n?a?w(!0):C=h.run():l?l(w.bind(null,!0),!0):h.run(),S.pause=h.pause.bind(h),S.resume=h.resume.bind(h),S.stop=S,S}function $t(e,t=1/0,n){if(t<=0||!_(e)||e.__v_skip||(n||=new Set,n.has(e)))return e;if(n.add(e),t--,L(e))$t(e.value,t,n);else if(d(e))for(let r=0;r<e.length;r++)$t(e[r],t,n);else if(p(e)||f(e))e.forEach(e=>{$t(e,t,n)});else if(S(e)){for(let r in e)$t(e[r],t,n);for(let r of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,r)&&$t(e[r],t,n)}return e}function en(e,t,n,r){try{return r?e(...r):e()}catch(e){nn(e,t,n)}}function tn(e,t,n,r){if(m(e)){let i=en(e,t,n,r);return i&&v(i)&&i.catch(e=>{nn(e,t,n)}),i}if(d(e)){let i=[];for(let a=0;a<e.length;a++)i.push(tn(e[a],t,n,r));return i}}function nn(e,n,r,i=!0){let a=n?n.vnode:null,{errorHandler:o,throwUnhandledErrorInProduction:s}=n&&n.appContext.config||t;if(n){let t=n.parent,i=n.proxy,a=`https://vuejs.org/error-reference/#runtime-${r}`;for(;t;){let n=t.ec;if(n){for(let t=0;t<n.length;t++)if(n[t](e,i,a)===!1)return}t=t.parent}if(o){ze(),en(o,null,10,[e,i,a]),Be();return}}rn(e,r,a,i,s)}function rn(e,t,n,r=!0,i=!1){if(i)throw e;console.error(e)}const R=[];let an=-1;const on=[];let sn=null,cn=0;const ln=Promise.resolve();let un=null;function dn(e){let t=un||ln;return e?t.then(this?e.bind(this):e):t}function fn(e){let t=an+1,n=R.length;for(;t<n;){let r=t+n>>>1,i=R[r],a=vn(i);a<e||a===e&&i.flags&2?t=r+1:n=r}return t}function pn(e){if(!(e.flags&1)){let t=vn(e),n=R[R.length-1];!n||!(e.flags&2)&&t>=vn(n)?R.push(e):R.splice(fn(t),0,e),e.flags|=1,mn()}}function mn(){un||=ln.then(yn)}function hn(e){d(e)?on.push(...e):sn&&e.id===-1?sn.splice(cn+1,0,e):e.flags&1||(on.push(e),e.flags|=1),mn()}function gn(e,t,n=an+1){for(;n<R.length;n++){let t=R[n];if(t&&t.flags&2){if(e&&t.id!==e.uid)continue;R.splice(n,1),n--,t.flags&4&&(t.flags&=-2),t(),t.flags&4||(t.flags&=-2)}}}function _n(e){if(on.length){let e=[...new Set(on)].sort((e,t)=>vn(e)-vn(t));if(on.length=0,sn){sn.push(...e);return}for(sn=e,cn=0;cn<sn.length;cn++){let e=sn[cn];e.flags&4&&(e.flags&=-2),e.flags&8||e(),e.flags&=-2}sn=null,cn=0}}const vn=e=>e.id==null?e.flags&2?-1:1/0:e.id;function yn(e){try{for(an=0;an<R.length;an++){let e=R[an];e&&!(e.flags&8)&&(e.flags&4&&(e.flags&=-2),en(e,e.i,e.i?15:14),e.flags&4||(e.flags&=-2))}}finally{for(;an<R.length;an++){let e=R[an];e&&(e.flags&=-2)}an=-1,R.length=0,_n(e),un=null,(R.length||on.length)&&yn(e)}}let z=null,bn=null;function xn(e){let t=z;return z=e,bn=e&&e.type.__scopeId||null,t}function B(e,t=z,n){if(!t||e._n)return e;let r=(...n)=>{r._d&&vi(-1);let i=xn(t),a;try{a=e(...n)}finally{xn(i),r._d&&vi(1)}return a};return r._n=!0,r._c=!0,r._d=!0,r}function Sn(e,t,n,r){let i=e.dirs,a=t&&t.dirs;for(let o=0;o<i.length;o++){let s=i[o];a&&(s.oldValue=a[o].value);let c=s.dir[r];c&&(ze(),tn(c,n,8,[e.el,s,e,t]),Be())}}const Cn=Symbol(`_vte`),wn=e=>e.__isTeleport;Symbol(`_leaveCb`),Symbol(`_enterCb`);function Tn(e,t){e.shapeFlag&6&&e.component?(e.transition=t,Tn(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}
/*! #__NO_SIDE_EFFECTS__ */
function En(e,t){return m(e)?(()=>s({name:e.name},t,{setup:e}))():e}function Dn(e){e.ids=[e.ids[0]+ e.ids[2]+++`-`,0,0]}function On(e,n,r,i,a=!1){if(d(e)){e.forEach((e,t)=>On(e,n&&(d(n)?n[t]:n),r,i,a));return}if(kn(i)&&!a){i.shapeFlag&512&&i.type.__asyncResolved&&i.component.subTree.component&&On(e,n,r,i.component.subTree);return}let o=i.shapeFlag&4?Zi(i.component):i.el,s=a?null:o,{i:l,r:f}=e,p=n&&n.r,g=l.refs===t?l.refs={}:l.refs,_=l.setupState,v=F(_),y=_===t?()=>!1:e=>u(v,e);if(p!=null&&p!==f&&(h(p)?(g[p]=null,y(p)&&(_[p]=null)):L(p)&&(p.value=null)),m(f))en(f,l,12,[s,g]);else{let t=h(f),n=L(f);if(t||n){let i=()=>{if(e.f){let n=t?y(f)?_[f]:g[f]:f.value;a?d(n)&&c(n,o):d(n)?n.includes(o)||n.push(o):t?(g[f]=[o],y(f)&&(_[f]=g[f])):(f.value=[o],e.k&&(g[e.k]=f.value))}else t?(g[f]=s,y(f)&&(_[f]=s)):n&&(f.value=s,e.k&&(g[e.k]=s))};s?(i.id=-1,H(i,r)):i()}}}se().requestIdleCallback,se().cancelIdleCallback;const kn=e=>!!e.type.__asyncLoader,An=e=>e.type.__isKeepAlive;function jn(e,t){Nn(e,`a`,t)}function Mn(e,t){Nn(e,`da`,t)}function Nn(e,t,n=X){let r=e.__wdc||=()=>{let t=n;for(;t;){if(t.isDeactivated)return;t=t.parent}return e()};if(Fn(t,r,n),n){let e=n.parent;for(;e&&e.parent;)An(e.parent.vnode)&&Pn(r,t,n,e),e=e.parent}}function Pn(e,t,n,r){let i=Fn(t,e,r,!0);Hn(()=>{c(r[t],i)},n)}function Fn(e,t,n=X,r=!1){if(n){let i=n[e]||(n[e]=[]),a=t.__weh||=(...r)=>{ze();let i=zi(n),a=tn(t,n,e,r);return i(),Be(),a};return r?i.unshift(a):i.push(a),a}}const In=e=>(t,n=X)=>{(!Hi||e===`sp`)&&Fn(e,(...e)=>t(...e),n)},Ln=In(`bm`),Rn=In(`m`),zn=In(`bu`),Bn=In(`u`),Vn=In(`bum`),Hn=In(`um`),Un=In(`sp`),Wn=In(`rtg`),Gn=In(`rtc`);function Kn(e,t=X){Fn(`ec`,e,t)}const qn=Symbol.for(`v-ndc`);function Jn(e,t,n={},r,i){if(z.ce||z.parent&&kn(z.parent)&&z.parent.ce)return t!==`default`&&(n.name=t),G(),bi(U,null,[J(`slot`,n,r&&r())],64);let a=e[t];a&&a._c&&(a._d=!1),G();let o=a&&Yn(a(n)),s=n.key||o&&o.key,c=bi(U,{key:(s&&!g(s)?s:`_${t}`)+(!o&&r?`_fb`:``)},o||(r?r():[]),o&&e._===1?64:-2);return!i&&c.scopeId&&(c.slotScopeIds=[c.scopeId+`-s`]),a&&a._c&&(a._d=!0),c}function Yn(e){return e.some(e=>xi(e)?!(e.type===pi||e.type===U&&!Yn(e.children)):!0)?e:null}const Xn=e=>e?Vi(e)?Zi(e):Xn(e.parent):null,Zn=s(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>Xn(e.parent),$root:e=>Xn(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>or(e),$forceUpdate:e=>e.f||=()=>{pn(e.update)},$nextTick:e=>e.n||=dn.bind(e.proxy),$watch:e=>Qr.bind(e)}),Qn=(e,n)=>e!==t&&!e.__isScriptSetup&&u(e,n),$n={get({_:e},n){if(n===`__v_skip`)return!0;let{ctx:r,setupState:i,data:a,props:o,accessCache:s,type:c,appContext:l}=e,d;if(n[0]!==`$`){let c=s[n];if(c!==void 0)switch(c){case 1:return i[n];case 2:return a[n];case 4:return r[n];case 3:return o[n]}else if(Qn(i,n))return s[n]=1,i[n];else if(a!==t&&u(a,n))return s[n]=2,a[n];else if((d=e.propsOptions[0])&&u(d,n))return s[n]=3,o[n];else if(r!==t&&u(r,n))return s[n]=4,r[n];else tr&&(s[n]=0)}let f=Zn[n],p,m;if(f)return n===`$attrs`&&N(e.attrs,`get`,``),f(e);if((p=c.__cssModules)&&(p=p[n]))return p;if(r!==t&&u(r,n))return s[n]=4,r[n];if(m=l.config.globalProperties,u(m,n))return m[n]},set({_:e},n,r){let{data:i,setupState:a,ctx:o}=e;return Qn(a,n)?(a[n]=r,!0):i!==t&&u(i,n)?(i[n]=r,!0):u(e.props,n)||n[0]===`$`&&n.slice(1)in e?!1:(o[n]=r,!0)},has({_:{data:e,setupState:n,accessCache:r,ctx:i,appContext:a,propsOptions:o}},s){let c;return!!r[s]||e!==t&&u(e,s)||Qn(n,s)||(c=o[0])&&u(c,s)||u(i,s)||u(Zn,s)||u(a.config.globalProperties,s)},defineProperty(e,t,n){return n.get==null?u(n,`value`)&&this.set(e,t,n.value,null):e._.accessCache[t]=0,Reflect.defineProperty(e,t,n)}};function er(e){return d(e)?e.reduce((e,t)=>(e[t]=null,e),{}):e}let tr=!0;function nr(e){let t=or(e),n=e.proxy,i=e.ctx;tr=!1,t.beforeCreate&&ir(t.beforeCreate,e,`bc`);let{data:a,computed:o,methods:s,watch:c,provide:l,inject:u,created:f,beforeMount:p,mounted:h,beforeUpdate:g,updated:v,activated:y,deactivated:b,beforeDestroy:x,beforeUnmount:S,destroyed:C,unmounted:w,render:ee,renderTracked:te,renderTriggered:T,errorCaptured:ne,serverPrefetch:E,expose:re,inheritAttrs:D,components:O,directives:ie,filters:ae}=t,oe=null;if(u&&rr(u,i,oe),s)for(let e in s){let t=s[e];m(t)&&(i[e]=t.bind(n))}if(a){let t=a.call(n,n);_(t)&&(e.data=At(t))}if(tr=!0,o)for(let e in o){let t=o[e],a=m(t)?t.bind(n,n):m(t.get)?t.get.bind(n,n):r,s=!m(t)&&m(t.set)?t.set.bind(n):r,c=Z({get:a,set:s});Object.defineProperty(i,e,{enumerable:!0,configurable:!0,get:()=>c.value,set:e=>c.value=e})}if(c)for(let e in c)ar(c[e],i,n,e);if(l){let e=m(l)?l.call(n):l;Reflect.ownKeys(e).forEach(t=>{yr(t,e[t])})}f&&ir(f,e,`c`);function k(e,t){d(t)?t.forEach(t=>e(t.bind(n))):t&&e(t.bind(n))}if(k(Ln,p),k(Rn,h),k(zn,g),k(Bn,v),k(jn,y),k(Mn,b),k(Kn,ne),k(Gn,te),k(Wn,T),k(Vn,S),k(Hn,w),k(Un,E),d(re))if(re.length){let t=e.exposed||={};re.forEach(e=>{Object.defineProperty(t,e,{get:()=>n[e],set:t=>n[e]=t,enumerable:!0})})}else e.exposed||={};ee&&e.render===r&&(e.render=ee),D!=null&&(e.inheritAttrs=D),O&&(e.components=O),ie&&(e.directives=ie),E&&Dn(e)}function rr(e,t,n=r){for(let n in d(e)&&(e=dr(e)),e){let r=e[n],i;i=_(r)?`default`in r?br(r.from||n,r.default,!0):br(r.from||n):br(r),L(i)?Object.defineProperty(t,n,{enumerable:!0,configurable:!0,get:()=>i.value,set:e=>i.value=e}):t[n]=i}}function ir(e,t,n){tn(d(e)?e.map(e=>e.bind(t.proxy)):e.bind(t.proxy),t,n)}function ar(e,t,n,r){let i=r.includes(`.`)?$r(n,r):()=>n[r];if(h(e)){let n=t[e];m(n)&&Xr(i,n)}else if(m(e))Xr(i,e.bind(n));else if(_(e))if(d(e))e.forEach(e=>ar(e,t,n,r));else{let r=m(e.handler)?e.handler.bind(n):t[e.handler];m(r)&&Xr(i,r,e)}}function or(e){let t=e.type,{mixins:n,extends:r}=t,{mixins:i,optionsCache:a,config:{optionMergeStrategies:o}}=e.appContext,s=a.get(t),c;return s?c=s:!i.length&&!n&&!r?c=t:(c={},i.length&&i.forEach(e=>sr(c,e,o,!0)),sr(c,t,o)),_(t)&&a.set(t,c),c}function sr(e,t,n,r=!1){let{mixins:i,extends:a}=t;for(let o in a&&sr(e,a,n,!0),i&&i.forEach(t=>sr(e,t,n,!0)),t)if(!(r&&o===`expose`)){let r=cr[o]||n&&n[o];e[o]=r?r(e[o],t[o]):t[o]}return e}const cr={data:lr,props:pr,emits:pr,methods:fr,computed:fr,beforeCreate:V,created:V,beforeMount:V,mounted:V,beforeUpdate:V,updated:V,beforeDestroy:V,beforeUnmount:V,destroyed:V,unmounted:V,activated:V,deactivated:V,errorCaptured:V,serverPrefetch:V,components:fr,directives:fr,watch:mr,provide:lr,inject:ur};function lr(e,t){return t?e?function(){return s(m(e)?e.call(this,this):e,m(t)?t.call(this,this):t)}:t:e}function ur(e,t){return fr(dr(e),dr(t))}function dr(e){if(d(e)){let t={};for(let n=0;n<e.length;n++)t[e[n]]=e[n];return t}return e}function V(e,t){return e?[...new Set([].concat(e,t))]:t}function fr(e,t){return e?s(Object.create(null),e,t):t}function pr(e,t){return e?d(e)&&d(t)?[...new Set([...e,...t])]:s(Object.create(null),er(e),er(t??{})):t}function mr(e,t){if(!e)return t;if(!t)return e;let n=s(Object.create(null),e);for(let r in t)n[r]=V(e[r],t[r]);return n}function hr(){return{app:null,config:{isNativeTag:i,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let gr=0;function _r(e,t){return function(n,r=null){m(n)||(n=s({},n)),r!=null&&!_(r)&&(r=null);let i=hr(),a=new WeakSet,o=[],c=!1,l=i.app={_uid:gr++,_component:n,_props:r,_container:null,_context:i,_instance:null,version:ea,get config(){return i.config},set config(e){},use(e,...t){return a.has(e)||(e&&m(e.install)?(a.add(e),e.install(l,...t)):m(e)&&(a.add(e),e(l,...t))),l},mixin(e){return i.mixins.includes(e)||i.mixins.push(e),l},component(e,t){return t?(i.components[e]=t,l):i.components[e]},directive(e,t){return t?(i.directives[e]=t,l):i.directives[e]},mount(a,o,s){if(!c){let u=l._ceVNode||J(n,r);return u.appContext=i,s===!0?s=`svg`:s===!1&&(s=void 0),o&&t?t(u,a):e(u,a,s),c=!0,l._container=a,a.__vue_app__=l,Zi(u.component)}},onUnmount(e){o.push(e)},unmount(){c&&(tn(o,l._instance,16),e(null,l._container),delete l._container.__vue_app__)},provide(e,t){return i.provides[e]=t,l},runWithContext(e){let t=vr;vr=l;try{return e()}finally{vr=t}}};return l}}let vr=null;function yr(e,t){if(X){let n=X.provides,r=X.parent&&X.parent.provides;r===n&&(n=X.provides=Object.create(r)),n[e]=t}}function br(e,t,n=!1){let r=Ii();if(r||vr){let i=vr?vr._context.provides:r?r.parent==null||r.ce?r.vnode.appContext&&r.vnode.appContext.provides:r.parent.provides:void 0;if(i&&e in i)return i[e];if(arguments.length>1)return n&&m(t)?t.call(r&&r.proxy):t}}const xr={},Sr=()=>Object.create(xr),Cr=e=>Object.getPrototypeOf(e)===xr;function wr(e,t,n,r=!1){let i={},a=Sr();for(let n in e.propsDefaults=Object.create(null),Er(e,t,i,a),e.propsOptions[0])n in i||(i[n]=void 0);n?e.props=r?i:jt(i):e.type.props?e.props=i:e.props=a,e.attrs=a}function Tr(e,t,n,r){let{props:i,attrs:a,vnode:{patchFlag:o}}=e,s=F(i),[c]=e.propsOptions,l=!1;if((r||o>0)&&!(o&16)){if(o&8){let n=e.vnode.dynamicProps;for(let r=0;r<n.length;r++){let o=n[r];if(ri(e.emitsOptions,o))continue;let d=t[o];if(c)if(u(a,o))d!==a[o]&&(a[o]=d,l=!0);else{let t=T(o);i[t]=Dr(c,s,t,d,e,!1)}else d!==a[o]&&(a[o]=d,l=!0)}}}else{Er(e,t,i,a)&&(l=!0);let r;for(let a in s)(!t||!u(t,a)&&((r=E(a))===a||!u(t,r)))&&(c?n&&(n[a]!==void 0||n[r]!==void 0)&&(i[a]=Dr(c,s,a,void 0,e,!0)):delete i[a]);if(a!==s)for(let e in a)(!t||!u(t,e))&&(delete a[e],l=!0)}l&&Xe(e.attrs,`set`,``)}function Er(e,n,r,i){let[a,o]=e.propsOptions,s=!1,c;if(n)for(let t in n){if(w(t))continue;let l=n[t],d;a&&u(a,d=T(t))?!o||!o.includes(d)?r[d]=l:(c||={})[d]=l:ri(e.emitsOptions,t)||(!(t in i)||l!==i[t])&&(i[t]=l,s=!0)}if(o){let n=F(r),i=c||t;for(let t=0;t<o.length;t++){let s=o[t];r[s]=Dr(a,n,s,i[s],e,!u(i,s))}}return s}function Dr(e,t,n,r,i,a){let o=e[n];if(o!=null){let e=u(o,`default`);if(e&&r===void 0){let e=o.default;if(o.type!==Function&&!o.skipFactory&&m(e)){let{propsDefaults:a}=i;if(n in a)r=a[n];else{let o=zi(i);r=a[n]=e.call(null,t),o()}}else r=e;i.ce&&i.ce._setProp(n,r)}o[0]&&(a&&!e?r=!1:o[1]&&(r===``||r===E(n))&&(r=!0))}return r}const Or=new WeakMap;function kr(e,r,i=!1){let a=i?Or:r.propsCache,o=a.get(e);if(o)return o;let c=e.props,l={},f=[],p=!1;if(!m(e)){let t=e=>{p=!0;let[t,n]=kr(e,r,!0);s(l,t),n&&f.push(...n)};!i&&r.mixins.length&&r.mixins.forEach(t),e.extends&&t(e.extends),e.mixins&&e.mixins.forEach(t)}if(!c&&!p)return _(e)&&a.set(e,n),n;if(d(c))for(let e=0;e<c.length;e++){let n=T(c[e]);Ar(n)&&(l[n]=t)}else if(c)for(let e in c){let t=T(e);if(Ar(t)){let n=c[e],r=l[t]=d(n)||m(n)?{type:n}:s({},n),i=r.type,a=!1,o=!0;if(d(i))for(let e=0;e<i.length;++e){let t=i[e],n=m(t)&&t.name;if(n===`Boolean`){a=!0;break}else n===`String`&&(o=!1)}else a=m(i)&&i.name===`Boolean`;r[0]=a,r[1]=o,(a||u(r,`default`))&&f.push(t)}}let h=[l,f];return _(e)&&a.set(e,h),h}function Ar(e){return e[0]!==`$`&&!w(e)}const jr=e=>e===`_`||e===`__`||e===`_ctx`||e===`$stable`,Mr=e=>d(e)?e.map(Oi):[Oi(e)],Nr=(e,t,n)=>{if(t._n)return t;let r=B((...e)=>Mr(t(...e)),n);return r._c=!1,r},Pr=(e,t,n)=>{let r=e._ctx;for(let n in e){if(jr(n))continue;let i=e[n];if(m(i))t[n]=Nr(n,i,r);else if(i!=null){let e=Mr(i);t[n]=()=>e}}},Fr=(e,t)=>{let n=Mr(t);e.slots.default=()=>n},Ir=(e,t,n)=>{for(let r in t)(n||!jr(r))&&(e[r]=t[r])},Lr=(e,t,n)=>{let r=e.slots=Sr();if(e.vnode.shapeFlag&32){let e=t.__;e&&ae(r,`__`,e,!0);let i=t._;i?(Ir(r,t,n),n&&ae(r,`_`,i,!0)):Pr(t,r)}else t&&Fr(e,t)},Rr=(e,n,r)=>{let{vnode:i,slots:a}=e,o=!0,s=t;if(i.shapeFlag&32){let e=n._;e?r&&e===1?o=!1:Ir(a,n,r):(o=!n.$stable,Pr(n,a)),s=n}else n&&(Fr(e,n),s={default:1});if(o)for(let e in a)!jr(e)&&s[e]==null&&delete a[e]},H=di;function zr(e){return Br(e)}function Br(e,i){let a=se();a.__VUE__=!0;let{insert:o,remove:s,patchProp:c,createElement:l,createText:u,createComment:f,setText:p,setElementText:m,parentNode:h,nextSibling:g,setScopeId:_=r,insertStaticContent:v}=e,y=(e,t,n,r=null,i=null,a=null,o=void 0,s=null,c=!!t.dynamicChildren)=>{if(e===t)return;e&&!Si(e,t)&&(r=be(e),he(e,i,a,!0),e=null),t.patchFlag===-2&&(c=!1,t.dynamicChildren=null);let{type:l,ref:u,shapeFlag:d}=t;switch(l){case fi:b(e,t,n,r);break;case pi:x(e,t,n,r);break;case mi:e??S(t,n,r,o);break;case U:ae(e,t,n,r,i,a,o,s,c);break;default:d&1?te(e,t,n,r,i,a,o,s,c):d&6?oe(e,t,n,r,i,a,o,s,c):(d&64||d&128)&&l.process(e,t,n,r,i,a,o,s,c,Se)}u!=null&&i?On(u,e&&e.ref,a,t||e,!t):u==null&&e&&e.ref!=null&&On(e.ref,null,a,e,!0)},b=(e,t,n,r)=>{if(e==null)o(t.el=u(t.children),n,r);else{let n=t.el=e.el;t.children!==e.children&&p(n,t.children)}},x=(e,t,n,r)=>{e==null?o(t.el=f(t.children||``),n,r):t.el=e.el},S=(e,t,n,r)=>{[e.el,e.anchor]=v(e.children,t,n,r,e.el,e.anchor)},C=({el:e,anchor:t},n,r)=>{let i;for(;e&&e!==t;)i=g(e),o(e,n,r),e=i;o(t,n,r)},ee=({el:e,anchor:t})=>{let n;for(;e&&e!==t;)n=g(e),s(e),e=n;s(t)},te=(e,t,n,r,i,a,o,s,c)=>{t.type===`svg`?o=`svg`:t.type===`math`&&(o=`mathml`),e==null?T(t,n,r,i,a,o,s,c):re(e,t,i,a,o,s,c)},T=(e,t,n,r,i,a,s,u)=>{let d,f,{props:p,shapeFlag:h,transition:g,dirs:_}=e;if(d=e.el=l(e.type,a,p&&p.is,p),h&8?m(d,e.children):h&16&&E(e.children,d,null,r,i,Vr(e,a),s,u),_&&Sn(e,null,r,`created`),ne(d,e,e.scopeId,s,r),p){for(let e in p)e!==`value`&&!w(e)&&c(d,e,null,p[e],a,r);`value`in p&&c(d,`value`,null,p.value,a),(f=p.onVnodeBeforeMount)&&Mi(f,r,e)}_&&Sn(e,null,r,`beforeMount`);let v=Ur(i,g);v&&g.beforeEnter(d),o(d,t,n),((f=p&&p.onVnodeMounted)||v||_)&&H(()=>{f&&Mi(f,r,e),v&&g.enter(d),_&&Sn(e,null,r,`mounted`)},i)},ne=(e,t,n,r,i)=>{if(n&&_(e,n),r)for(let t=0;t<r.length;t++)_(e,r[t]);if(i){let n=i.subTree;if(t===n||ui(n.type)&&(n.ssContent===t||n.ssFallback===t)){let t=i.vnode;ne(e,t,t.scopeId,t.slotScopeIds,i.parent)}}},E=(e,t,n,r,i,a,o,s,c=0)=>{for(let l=c;l<e.length;l++){let c=e[l]=s?ki(e[l]):Oi(e[l]);y(null,c,t,n,r,i,a,o,s)}},re=(e,n,r,i,a,o,s)=>{let l=n.el=e.el,{patchFlag:u,dynamicChildren:d,dirs:f}=n;u|=e.patchFlag&16;let p=e.props||t,h=n.props||t,g;if(r&&Hr(r,!1),(g=h.onVnodeBeforeUpdate)&&Mi(g,r,n,e),f&&Sn(n,e,r,`beforeUpdate`),r&&Hr(r,!0),(p.innerHTML&&h.innerHTML==null||p.textContent&&h.textContent==null)&&m(l,``),d?D(e.dynamicChildren,d,l,r,i,Vr(n,a),o):s||de(e,n,l,null,r,i,Vr(n,a),o,!1),u>0){if(u&16)O(l,p,h,r,a);else if(u&2&&p.class!==h.class&&c(l,`class`,null,h.class,a),u&4&&c(l,`style`,p.style,h.style,a),u&8){let e=n.dynamicProps;for(let t=0;t<e.length;t++){let n=e[t],i=p[n],o=h[n];(o!==i||n===`value`)&&c(l,n,i,o,a,r)}}u&1&&e.children!==n.children&&m(l,n.children)}else !s&&d==null&&O(l,p,h,r,a);((g=h.onVnodeUpdated)||f)&&H(()=>{g&&Mi(g,r,n,e),f&&Sn(n,e,r,`updated`)},i)},D=(e,t,n,r,i,a,o)=>{for(let s=0;s<t.length;s++){let c=e[s],l=t[s],u=c.el&&(c.type===U||!Si(c,l)||c.shapeFlag&198)?h(c.el):n;y(c,l,u,null,r,i,a,o,!0)}},O=(e,n,r,i,a)=>{if(n!==r){if(n!==t)for(let t in n)!w(t)&&!(t in r)&&c(e,t,n[t],null,a,i);for(let t in r){if(w(t))continue;let o=r[t],s=n[t];o!==s&&t!==`value`&&c(e,t,s,o,a,i)}`value`in r&&c(e,`value`,n.value,r.value,a)}},ae=(e,t,n,r,i,a,s,c,l)=>{let d=t.el=e?e.el:u(``),f=t.anchor=e?e.anchor:u(``),{patchFlag:p,dynamicChildren:m,slotScopeIds:h}=t;h&&(c=c?c.concat(h):h),e==null?(o(d,n,r),o(f,n,r),E(t.children||[],n,f,i,a,s,c,l)):p>0&&p&64&&m&&e.dynamicChildren?(D(e.dynamicChildren,m,n,i,a,s,c),(t.key!=null||i&&t===i.subTree)&&Wr(e,t,!0)):de(e,t,n,f,i,a,s,c,l)},oe=(e,t,n,r,i,a,o,s,c)=>{t.slotScopeIds=s,e==null?t.shapeFlag&512?i.ctx.activate(t,n,r,o,c):k(t,n,r,i,a,o,c):ce(e,t,c)},k=(e,t,n,r,i,a,o)=>{let s=e.component=Fi(e,r,i);if(An(e)&&(s.ctx.renderer=Se),Ui(s,!1,o),s.asyncDep){if(i&&i.registerDep(s,le,o),!e.el){let r=s.subTree=J(pi);x(null,r,t,n),e.placeholder=r.el}}else le(s,e,t,n,i,a,o)},ce=(e,t,n)=>{let r=t.component=e.component;if(si(e,t,n))if(r.asyncDep&&!r.asyncResolved){ue(r,t,n);return}else r.next=t,r.update();else t.el=e.el,r.vnode=t},le=(e,t,n,r,i,a,o)=>{let s=()=>{if(e.isMounted){let{next:t,bu:n,u:r,parent:c,vnode:l}=e;{let n=Kr(e);if(n){t&&(t.el=l.el,ue(e,t,o)),n.asyncDep.then(()=>{e.isUnmounted||s()});return}}let u=t,d;Hr(e,!1),t?(t.el=l.el,ue(e,t,o)):t=l,n&&ie(n),(d=t.props&&t.props.onVnodeBeforeUpdate)&&Mi(d,c,t,l),Hr(e,!0);let f=ii(e),p=e.subTree;e.subTree=f,y(p,f,h(p.el),be(p),e,i,a),t.el=f.el,u===null&&li(e,f.el),r&&H(r,i),(d=t.props&&t.props.onVnodeUpdated)&&H(()=>Mi(d,c,t,l),i)}else{let o,{el:s,props:c}=t,{bm:l,m:u,parent:d,root:f,type:p}=e,m=kn(t);if(Hr(e,!1),l&&ie(l),!m&&(o=c&&c.onVnodeBeforeMount)&&Mi(o,d,t),Hr(e,!0),s&&j){let t=()=>{e.subTree=ii(e),j(s,e.subTree,e,i,null)};m&&p.__asyncHydrate?p.__asyncHydrate(s,e,t):t()}else{f.ce&&f.ce._def.shadowRoot!==!1&&f.ce._injectChildStyle(p);let o=e.subTree=ii(e);y(null,o,n,r,e,i,a),t.el=o.el}if(u&&H(u,i),!m&&(o=c&&c.onVnodeMounted)){let e=t;H(()=>Mi(o,d,e),i)}(t.shapeFlag&256||d&&kn(d.vnode)&&d.vnode.shapeFlag&256)&&e.a&&H(e.a,i),e.isMounted=!0,t=n=r=null}};e.scope.on();let c=e.effect=new Te(s);e.scope.off();let l=e.update=c.run.bind(c),u=e.job=c.runIfDirty.bind(c);u.i=e,u.id=e.uid,c.scheduler=()=>pn(u),Hr(e,!0),l()},ue=(e,t,n)=>{t.component=e;let r=e.vnode.props;e.vnode=t,e.next=null,Tr(e,t.props,r,n),Rr(e,t.children,n),ze(),gn(e),Be()},de=(e,t,n,r,i,a,o,s,c=!1)=>{let l=e&&e.children,u=e?e.shapeFlag:0,d=t.children,{patchFlag:f,shapeFlag:p}=t;if(f>0){if(f&128){pe(l,d,n,r,i,a,o,s,c);return}else if(f&256){fe(l,d,n,r,i,a,o,s,c);return}}p&8?(u&16&&ye(l,i,a),d!==l&&m(n,d)):u&16?p&16?pe(l,d,n,r,i,a,o,s,c):ye(l,i,a,!0):(u&8&&m(n,``),p&16&&E(d,n,r,i,a,o,s,c))},fe=(e,t,r,i,a,o,s,c,l)=>{e||=n,t||=n;let u=e.length,d=t.length,f=Math.min(u,d),p;for(p=0;p<f;p++){let n=t[p]=l?ki(t[p]):Oi(t[p]);y(e[p],n,r,null,a,o,s,c,l)}u>d?ye(e,a,o,!0,!1,f):E(t,r,i,a,o,s,c,l,f)},pe=(e,t,r,i,a,o,s,c,l)=>{let u=0,d=t.length,f=e.length-1,p=d-1;for(;u<=f&&u<=p;){let n=e[u],i=t[u]=l?ki(t[u]):Oi(t[u]);if(Si(n,i))y(n,i,r,null,a,o,s,c,l);else break;u++}for(;u<=f&&u<=p;){let n=e[f],i=t[p]=l?ki(t[p]):Oi(t[p]);if(Si(n,i))y(n,i,r,null,a,o,s,c,l);else break;f--,p--}if(u>f){if(u<=p){let e=p+1,n=e<d?t[e].el:i;for(;u<=p;)y(null,t[u]=l?ki(t[u]):Oi(t[u]),r,n,a,o,s,c,l),u++}}else if(u>p)for(;u<=f;)he(e[u],a,o,!0),u++;else{let m=u,h=u,g=new Map;for(u=h;u<=p;u++){let e=t[u]=l?ki(t[u]):Oi(t[u]);e.key!=null&&g.set(e.key,u)}let _,v=0,b=p-h+1,x=!1,S=0,C=Array(b);for(u=0;u<b;u++)C[u]=0;for(u=m;u<=f;u++){let n=e[u];if(v>=b){he(n,a,o,!0);continue}let i;if(n.key!=null)i=g.get(n.key);else for(_=h;_<=p;_++)if(C[_-h]===0&&Si(n,t[_])){i=_;break}i===void 0?he(n,a,o,!0):(C[i-h]=u+1,i>=S?S=i:x=!0,y(n,t[i],r,null,a,o,s,c,l),v++)}let w=x?Gr(C):n;for(_=w.length-1,u=b-1;u>=0;u--){let e=h+u,n=t[e],f=t[e+1],p=e+1<d?f.el||f.placeholder:i;C[u]===0?y(null,n,r,p,a,o,s,c,l):x&&(_<0||u!==w[_]?me(n,r,p,2):_--)}}},me=(e,t,n,r,i=null)=>{let{el:a,type:c,transition:l,children:u,shapeFlag:d}=e;if(d&6){me(e.component.subTree,t,n,r);return}if(d&128){e.suspense.move(t,n,r);return}if(d&64){c.move(e,t,n,Se);return}if(c===U){o(a,t,n);for(let e=0;e<u.length;e++)me(u[e],t,n,r);o(e.anchor,t,n);return}if(c===mi){C(e,t,n);return}let f=r!==2&&d&1&&l;if(f)if(r===0)l.beforeEnter(a),o(a,t,n),H(()=>l.enter(a),i);else{let{leave:r,delayLeave:i,afterLeave:c}=l,u=()=>{e.ctx.isUnmounted?s(a):o(a,t,n)},d=()=>{r(a,()=>{u(),c&&c()})};i?i(a,u,d):d()}else o(a,t,n)},he=(e,t,n,r=!1,i=!1)=>{let{type:a,props:o,ref:s,children:c,dynamicChildren:l,shapeFlag:u,patchFlag:d,dirs:f,cacheIndex:p}=e;if(d===-2&&(i=!1),s!=null&&(ze(),On(s,null,n,e,!0),Be()),p!=null&&(t.renderCache[p]=void 0),u&256){t.ctx.deactivate(e);return}let m=u&1&&f,h=!kn(e),g;if(h&&(g=o&&o.onVnodeBeforeUnmount)&&Mi(g,t,e),u&6)ve(e.component,n,r);else{if(u&128){e.suspense.unmount(n,r);return}m&&Sn(e,null,t,`beforeUnmount`),u&64?e.type.remove(e,t,n,Se,r):l&&!l.hasOnce&&(a!==U||d>0&&d&64)?ye(l,t,n,!1,!0):(a===U&&d&384||!i&&u&16)&&ye(c,t,n),r&&ge(e)}(h&&(g=o&&o.onVnodeUnmounted)||m)&&H(()=>{g&&Mi(g,t,e),m&&Sn(e,null,t,`unmounted`)},n)},ge=e=>{let{type:t,el:n,anchor:r,transition:i}=e;if(t===U){_e(n,r);return}if(t===mi){ee(e);return}let a=()=>{s(n),i&&!i.persisted&&i.afterLeave&&i.afterLeave()};if(e.shapeFlag&1&&i&&!i.persisted){let{leave:t,delayLeave:r}=i,o=()=>t(n,a);r?r(e.el,a,o):o()}else a()},_e=(e,t)=>{let n;for(;e!==t;)n=g(e),s(e),e=n;s(t)},ve=(e,t,n)=>{let{bum:r,scope:i,job:a,subTree:o,um:s,m:c,a:l,parent:u,slots:{__:f}}=e;qr(c),qr(l),r&&ie(r),u&&d(f)&&f.forEach(e=>{u.renderCache[e]=void 0}),i.stop(),a&&(a.flags|=8,he(o,e,t,n)),s&&H(s,t),H(()=>{e.isUnmounted=!0},t),t&&t.pendingBranch&&!t.isUnmounted&&e.asyncDep&&!e.asyncResolved&&e.suspenseId===t.pendingId&&(t.deps--,t.deps===0&&t.resolve())},ye=(e,t,n,r=!1,i=!1,a=0)=>{for(let o=a;o<e.length;o++)he(e[o],t,n,r,i)},be=e=>{if(e.shapeFlag&6)return be(e.component.subTree);if(e.shapeFlag&128)return e.suspense.next();let t=g(e.anchor||e.el),n=t&&t[Cn];return n?g(n):t},A=!1,xe=(e,t,n)=>{e==null?t._vnode&&he(t._vnode,null,null,!0):y(t._vnode||null,e,t,null,null,null,n),t._vnode=e,A||(A=!0,gn(),_n(),A=!1)},Se={p:y,um:he,m:me,r:ge,mt:k,mc:E,pc:de,pbc:D,n:be,o:e},Ce,j;return i&&([Ce,j]=i(Se)),{render:xe,hydrate:Ce,createApp:_r(xe,Ce)}}function Vr({type:e,props:t},n){return n===`svg`&&e===`foreignObject`||n===`mathml`&&e===`annotation-xml`&&t&&t.encoding&&t.encoding.includes(`html`)?void 0:n}function Hr({effect:e,job:t},n){n?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function Ur(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function Wr(e,t,n=!1){let r=e.children,i=t.children;if(d(r)&&d(i))for(let e=0;e<r.length;e++){let t=r[e],a=i[e];a.shapeFlag&1&&!a.dynamicChildren&&((a.patchFlag<=0||a.patchFlag===32)&&(a=i[e]=ki(i[e]),a.el=t.el),!n&&a.patchFlag!==-2&&Wr(t,a)),a.type===fi&&(a.el=t.el),a.type===pi&&!a.el&&(a.el=t.el)}}function Gr(e){let t=e.slice(),n=[0],r,i,a,o,s,c=e.length;for(r=0;r<c;r++){let c=e[r];if(c!==0){if(i=n[n.length-1],e[i]<c){t[r]=i,n.push(r);continue}for(a=0,o=n.length-1;a<o;)s=a+o>>1,e[n[s]]<c?a=s+1:o=s;c<e[n[a]]&&(a>0&&(t[r]=n[a-1]),n[a]=r)}}for(a=n.length,o=n[a-1];a-- >0;)n[a]=o,o=t[o];return n}function Kr(e){let t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:Kr(t)}function qr(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const Jr=Symbol.for(`v-scx`),Yr=()=>{{let e=br(Jr);return e}};function Xr(e,t,n){return Zr(e,t,n)}function Zr(e,n,i=t){let{immediate:a,deep:o,flush:c,once:l}=i,u=s({},i),d=n&&a||!n&&c!==`post`,f;if(Hi){if(c===`sync`){let e=Yr();f=e.__watcherHandles||=[]}else if(!d){let e=()=>{};return e.stop=r,e.resume=r,e.pause=r,e}}let p=X;u.call=(e,t,n)=>tn(e,p,t,n);let m=!1;c===`post`?u.scheduler=e=>{H(e,p&&p.suspense)}:c!==`sync`&&(m=!0,u.scheduler=(e,t)=>{t?e():pn(e)}),u.augmentJob=e=>{n&&(e.flags|=4),m&&(e.flags|=2,p&&(e.id=p.uid,e.i=p))};let h=Qt(e,n,u);return Hi&&(f?f.push(h):d&&h()),h}function Qr(e,t,n){let r=this.proxy,i=h(e)?e.includes(`.`)?$r(r,e):()=>r[e]:e.bind(r,r),a;m(t)?a=t:(a=t.handler,n=t);let o=zi(this),s=Zr(i,a.bind(r),n);return o(),s}function $r(e,t){let n=t.split(`.`);return()=>{let t=e;for(let e=0;e<n.length&&t;e++)t=t[n[e]];return t}}const ei=(e,t)=>t===`modelValue`||t===`model-value`?e.modelModifiers:e[`${t}Modifiers`]||e[`${T(t)}Modifiers`]||e[`${E(t)}Modifiers`];function ti(e,n,...r){if(e.isUnmounted)return;let i=e.vnode.props||t,a=r,o=n.startsWith(`update:`),s=o&&ei(i,n.slice(7));s&&(s.trim&&(a=r.map(e=>h(e)?e.trim():e)),s.number&&(a=r.map(oe)));let c,l=i[c=D(n)]||i[c=D(T(n))];!l&&o&&(l=i[c=D(E(n))]),l&&tn(l,e,6,a);let u=i[c+`Once`];if(u){if(!e.emitted)e.emitted={};else if(e.emitted[c])return;e.emitted[c]=!0,tn(u,e,6,a)}}function ni(e,t,n=!1){let r=t.emitsCache,i=r.get(e);if(i!==void 0)return i;let a=e.emits,o={},c=!1;if(!m(e)){let r=e=>{let n=ni(e,t,!0);n&&(c=!0,s(o,n))};!n&&t.mixins.length&&t.mixins.forEach(r),e.extends&&r(e.extends),e.mixins&&e.mixins.forEach(r)}return!a&&!c?(_(e)&&r.set(e,null),null):(d(a)?a.forEach(e=>o[e]=null):s(o,a),_(e)&&r.set(e,o),o)}function ri(e,t){return!e||!a(t)?!1:(t=t.slice(2).replace(/Once$/,``),u(e,t[0].toLowerCase()+t.slice(1))||u(e,E(t))||u(e,t))}function ii(e){let{type:t,vnode:n,proxy:r,withProxy:i,propsOptions:[a],slots:s,attrs:c,emit:l,render:u,renderCache:d,props:f,data:p,setupState:m,ctx:h,inheritAttrs:g}=e,_=xn(e),v,y;try{if(n.shapeFlag&4){let e=i||r,t=e;v=Oi(u.call(t,e,d,f,m,p,h)),y=c}else{let e=t;v=Oi(e.length>1?e(f,{attrs:c,slots:s,emit:l}):e(f,null)),y=t.props?c:ai(c)}}catch(t){hi.length=0,nn(t,e,1),v=J(pi)}let b=v;if(y&&g!==!1){let e=Object.keys(y),{shapeFlag:t}=b;e.length&&t&7&&(a&&e.some(o)&&(y=oi(y,a)),b=Di(b,y,!1,!0))}return n.dirs&&(b=Di(b,null,!1,!0),b.dirs=b.dirs?b.dirs.concat(n.dirs):n.dirs),n.transition&&Tn(b,n.transition),v=b,xn(_),v}const ai=e=>{let t;for(let n in e)(n===`class`||n===`style`||a(n))&&((t||={})[n]=e[n]);return t},oi=(e,t)=>{let n={};for(let r in e)(!o(r)||!(r.slice(9)in t))&&(n[r]=e[r]);return n};function si(e,t,n){let{props:r,children:i,component:a}=e,{props:o,children:s,patchFlag:c}=t,l=a.emitsOptions;if(t.dirs||t.transition)return!0;if(n&&c>=0){if(c&1024)return!0;if(c&16)return r?ci(r,o,l):!!o;if(c&8){let e=t.dynamicProps;for(let t=0;t<e.length;t++){let n=e[t];if(o[n]!==r[n]&&!ri(l,n))return!0}}}else return(i||s)&&(!s||!s.$stable)?!0:r===o?!1:r?o?ci(r,o,l):!0:!!o;return!1}function ci(e,t,n){let r=Object.keys(t);if(r.length!==Object.keys(e).length)return!0;for(let i=0;i<r.length;i++){let a=r[i];if(t[a]!==e[a]&&!ri(n,a))return!0}return!1}function li({vnode:e,parent:t},n){for(;t;){let r=t.subTree;if(r.suspense&&r.suspense.activeBranch===e&&(r.el=e.el),r===e)(e=t.vnode).el=n,t=t.parent;else break}}const ui=e=>e.__isSuspense;function di(e,t){t&&t.pendingBranch?d(e)?t.effects.push(...e):t.effects.push(e):hn(e)}const U=Symbol.for(`v-fgt`),fi=Symbol.for(`v-txt`),pi=Symbol.for(`v-cmt`),mi=Symbol.for(`v-stc`),hi=[];let W=null;function G(e=!1){hi.push(W=e?null:[])}function gi(){hi.pop(),W=hi[hi.length-1]||null}let _i=1;function vi(e,t=!1){_i+=e,e<0&&W&&t&&(W.hasOnce=!0)}function yi(e){return e.dynamicChildren=_i>0?W||n:null,gi(),_i>0&&W&&W.push(e),e}function K(e,t,n,r,i,a){return yi(q(e,t,n,r,i,a,!0))}function bi(e,t,n,r,i){return yi(J(e,t,n,r,i,!0))}function xi(e){return e?e.__v_isVNode===!0:!1}function Si(e,t){return e.type===t.type&&e.key===t.key}const Ci=({key:e})=>e??null,wi=({ref:e,ref_key:t,ref_for:n})=>(typeof e==`number`&&(e=``+e),e==null?null:h(e)||L(e)||m(e)?{i:z,r:e,k:t,f:!!n}:e);function q(e,t=null,n=null,r=0,i=null,a=e===U?0:1,o=!1,s=!1){let c={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&Ci(t),ref:t&&wi(t),scopeId:bn,slotScopeIds:null,children:n,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:a,patchFlag:r,dynamicProps:i,dynamicChildren:null,appContext:null,ctx:z};return s?(Ai(c,n),a&128&&e.normalize(c)):n&&(c.shapeFlag|=h(n)?8:16),_i>0&&!o&&W&&(c.patchFlag>0||a&6)&&c.patchFlag!==32&&W.push(c),c}const J=Ti;function Ti(e,t=null,n=null,r=0,i=null,a=!1){if((!e||e===qn)&&(e=pi),xi(e)){let r=Di(e,t,!0);return n&&Ai(r,n),_i>0&&!a&&W&&(r.shapeFlag&6?W[W.indexOf(e)]=r:W.push(r)),r.patchFlag=-2,r}if(Qi(e)&&(e=e.__vccOpts),t){t=Ei(t);let{class:e,style:n}=t;e&&!h(e)&&(t.class=pe(e)),_(n)&&(It(n)&&!d(n)&&(n=s({},n)),t.style=ce(n))}let o=h(e)?1:ui(e)?128:wn(e)?64:_(e)?4:m(e)?2:0;return q(e,t,n,r,i,o,a,!0)}function Ei(e){return e?It(e)||Cr(e)?s({},e):e:null}function Di(e,t,n=!1,r=!1){let{props:i,ref:a,patchFlag:o,children:s,transition:c}=e,l=t?ji(i||{},t):i,u={__v_isVNode:!0,__v_skip:!0,type:e.type,props:l,key:l&&Ci(l),ref:t&&t.ref?n&&a?d(a)?a.concat(wi(t)):[a,wi(t)]:wi(t):a,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:s,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==U?o===-1?16:o|16:o,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:c,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&Di(e.ssContent),ssFallback:e.ssFallback&&Di(e.ssFallback),placeholder:e.placeholder,el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return c&&r&&Tn(u,c.clone(u)),u}function Y(e=` `,t=0){return J(fi,null,e,t)}function Oi(e){return e==null||typeof e==`boolean`?J(pi):d(e)?J(U,null,e.slice()):xi(e)?ki(e):J(fi,null,String(e))}function ki(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:Di(e)}function Ai(e,t){let n=0,{shapeFlag:r}=e;if(t==null)t=null;else if(d(t))n=16;else if(typeof t==`object`)if(r&65){let n=t.default;n&&(n._c&&(n._d=!1),Ai(e,n()),n._c&&(n._d=!0));return}else{n=32;let r=t._;!r&&!Cr(t)?t._ctx=z:r===3&&z&&(z.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else m(t)?(t={default:t,_ctx:z},n=32):(t=String(t),r&64?(n=16,t=[Y(t)]):n=8);e.children=t,e.shapeFlag|=n}function ji(...e){let t={};for(let n=0;n<e.length;n++){let r=e[n];for(let e in r)if(e===`class`)t.class!==r.class&&(t.class=pe([t.class,r.class]));else if(e===`style`)t.style=ce([t.style,r.style]);else if(a(e)){let n=t[e],i=r[e];i&&n!==i&&!(d(n)&&n.includes(i))&&(t[e]=n?[].concat(n,i):i)}else e!==``&&(t[e]=r[e])}return t}function Mi(e,t,n,r=null){tn(e,t,7,[n,r])}const Ni=hr();let Pi=0;function Fi(e,n,r){let i=e.type,a=(n?n.appContext:e.appContext)||Ni,o={uid:Pi++,vnode:e,type:i,parent:n,appContext:a,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new xe(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:n?n.provides:Object.create(a.provides),ids:n?n.ids:[``,0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:kr(i,a),emitsOptions:ni(i,a),emit:null,emitted:null,propsDefaults:t,inheritAttrs:i.inheritAttrs,ctx:t,data:t,props:t,attrs:t,slots:t,refs:t,setupState:t,setupContext:null,suspense:r,suspenseId:r?r.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return o.ctx={_:o},o.root=n?n.root:o,o.emit=ti.bind(null,o),e.ce&&e.ce(o),o}let X=null;const Ii=()=>X||z;let Li,Ri;{let e=se(),t=(t,n)=>{let r;return(r=e[t])||(r=e[t]=[]),r.push(n),e=>{r.length>1?r.forEach(t=>t(e)):r[0](e)}};Li=t(`__VUE_INSTANCE_SETTERS__`,e=>X=e),Ri=t(`__VUE_SSR_SETTERS__`,e=>Hi=e)}const zi=e=>{let t=X;return Li(e),e.scope.on(),()=>{e.scope.off(),Li(t)}},Bi=()=>{X&&X.scope.off(),Li(null)};function Vi(e){return e.vnode.shapeFlag&4}let Hi=!1;function Ui(e,t=!1,n=!1){t&&Ri(t);let{props:r,children:i}=e.vnode,a=Vi(e);wr(e,r,a,t),Lr(e,i,n||t);let o=a?Wi(e,t):void 0;return t&&Ri(!1),o}function Wi(e,t){let n=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,$n);let{setup:r}=n;if(r){ze();let n=e.setupContext=r.length>1?Xi(e):null,i=zi(e),a=en(r,e,0,[e.props,n]),o=v(a);if(Be(),i(),(o||e.sp)&&!kn(e)&&Dn(e),o){if(a.then(Bi,Bi),t)return a.then(n=>{Gi(e,n,t)}).catch(t=>{nn(t,e,0)});e.asyncDep=a}else Gi(e,a,t)}else Ji(e,t)}function Gi(e,t,n){m(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:_(t)&&(e.setupState=Gt(t)),Ji(e,n)}let Ki,qi;function Ji(e,t,n){let i=e.type;if(!e.render){if(!t&&Ki&&!i.render){let t=i.template||or(e).template;if(t){let{isCustomElement:n,compilerOptions:r}=e.appContext.config,{delimiters:a,compilerOptions:o}=i,c=s(s({isCustomElement:n,delimiters:a},r),o);i.render=Ki(t,c)}}e.render=i.render||r,qi&&qi(e)}{let t=zi(e);ze();try{nr(e)}finally{Be(),t()}}}const Yi={get(e,t){return N(e,`get`,``),e[t]}};function Xi(e){let t=t=>{e.exposed=t||{}};return{attrs:new Proxy(e.attrs,Yi),slots:e.slots,emit:e.emit,expose:t}}function Zi(e){return e.exposed?e.exposeProxy||=new Proxy(Gt(Lt(e.exposed)),{get(t,n){if(n in t)return t[n];if(n in Zn)return Zn[n](e)},has(e,t){return t in e||t in Zn}}):e.proxy}function Qi(e){return m(e)&&`__vccOpts`in e}const Z=(e,t)=>{let n=qt(e,t,Hi);return n};function $i(e,t,n){let r=arguments.length;return r===2?_(t)&&!d(t)?xi(t)?J(e,null,[t]):J(e,t):J(e,null,t):(r>3?n=Array.prototype.slice.call(arguments,2):r===3&&xi(n)&&(n=[n]),J(e,t,n))}const ea=`3.5.18`;let ta;const na=typeof window<`u`&&window.trustedTypes;if(na)try{ta=na.createPolicy(`vue`,{createHTML:e=>e})}catch{}const ra=ta?e=>ta.createHTML(e):e=>e,ia=`http://www.w3.org/2000/svg`,aa=`http://www.w3.org/1998/Math/MathML`,oa=typeof document<`u`?document:null,sa=oa&&oa.createElement(`template`),ca={insert:(e,t,n)=>{t.insertBefore(e,n||null)},remove:e=>{let t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,n,r)=>{let i=t===`svg`?oa.createElementNS(ia,e):t===`mathml`?oa.createElementNS(aa,e):n?oa.createElement(e,{is:n}):oa.createElement(e);return e===`select`&&r&&r.multiple!=null&&i.setAttribute(`multiple`,r.multiple),i},createText:e=>oa.createTextNode(e),createComment:e=>oa.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>oa.querySelector(e),setScopeId(e,t){e.setAttribute(t,``)},insertStaticContent(e,t,n,r,i,a){let o=n?n.previousSibling:t.lastChild;if(i&&(i===a||i.nextSibling))for(;t.insertBefore(i.cloneNode(!0),n),!(i===a||!(i=i.nextSibling)););else{sa.innerHTML=ra(r===`svg`?`<svg>${e}</svg>`:r===`mathml`?`<math>${e}</math>`:e);let i=sa.content;if(r===`svg`||r===`mathml`){let e=i.firstChild;for(;e.firstChild;)i.appendChild(e.firstChild);i.removeChild(e)}t.insertBefore(i,n)}return[o?o.nextSibling:t.firstChild,n?n.previousSibling:t.lastChild]}},la=Symbol(`_vtc`);function ua(e,t,n){let r=e[la];r&&(t=(t?[t,...r]:[...r]).join(` `)),t==null?e.removeAttribute(`class`):n?e.setAttribute(`class`,t):e.className=t}const da=Symbol(`_vod`),fa=Symbol(`_vsh`),pa=Symbol(``),ma=/(^|;)\s*display\s*:/;function ha(e,t,n){let r=e.style,i=h(n),a=!1;if(n&&!i){if(t)if(h(t))for(let e of t.split(`;`)){let t=e.slice(0,e.indexOf(`:`)).trim();n[t]??_a(r,t,``)}else for(let e in t)n[e]??_a(r,e,``);for(let e in n)e===`display`&&(a=!0),_a(r,e,n[e])}else if(i){if(t!==n){let e=r[pa];e&&(n+=`;`+e),r.cssText=n,a=ma.test(n)}}else t&&e.removeAttribute(`style`);da in e&&(e[da]=a?r.display:``,e[fa]&&(r.display=`none`))}const ga=/\s*!important$/;function _a(e,t,n){if(d(n))n.forEach(n=>_a(e,t,n));else if(n??=``,t.startsWith(`--`))e.setProperty(t,n);else{let r=ba(e,t);ga.test(n)?e.setProperty(E(r),n.replace(ga,``),`important`):e[r]=n}}const va=[`Webkit`,`Moz`,`ms`],ya={};function ba(e,t){let n=ya[t];if(n)return n;let r=T(t);if(r!==`filter`&&r in e)return ya[t]=r;r=re(r);for(let n=0;n<va.length;n++){let i=va[n]+r;if(i in e)return ya[t]=i}return t}const xa=`http://www.w3.org/1999/xlink`;function Sa(e,t,n,r,i,a=he(t)){r&&t.startsWith(`xlink:`)?n==null?e.removeAttributeNS(xa,t.slice(6,t.length)):e.setAttributeNS(xa,t,n):n==null||a&&!ge(n)?e.removeAttribute(t):e.setAttribute(t,a?``:g(n)?String(n):n)}function Ca(e,t,n,r,i){if(t===`innerHTML`||t===`textContent`){n!=null&&(e[t]=t===`innerHTML`?ra(n):n);return}let a=e.tagName;if(t===`value`&&a!==`PROGRESS`&&!a.includes(`-`)){let r=a===`OPTION`?e.getAttribute(`value`)||``:e.value,i=n==null?e.type===`checkbox`?`on`:``:String(n);(r!==i||!(`_value`in e))&&(e.value=i),n??e.removeAttribute(t),e._value=n;return}let o=!1;if(n===``||n==null){let r=typeof e[t];r===`boolean`?n=ge(n):n==null&&r===`string`?(n=``,o=!0):r===`number`&&(n=0,o=!0)}try{e[t]=n}catch{}o&&e.removeAttribute(i||t)}function wa(e,t,n,r){e.addEventListener(t,n,r)}function Ta(e,t,n,r){e.removeEventListener(t,n,r)}const Ea=Symbol(`_vei`);function Da(e,t,n,r,i=null){let a=e[Ea]||(e[Ea]={}),o=a[t];if(r&&o)o.value=r;else{let[n,s]=ka(t);if(r){let o=a[t]=Na(r,i);wa(e,n,o,s)}else o&&(Ta(e,n,o,s),a[t]=void 0)}}const Oa=/(?:Once|Passive|Capture)$/;function ka(e){let t;if(Oa.test(e)){t={};let n;for(;n=e.match(Oa);)e=e.slice(0,e.length-n[0].length),t[n[0].toLowerCase()]=!0}let n=e[2]===`:`?e.slice(3):E(e.slice(2));return[n,t]}let Aa=0;const ja=Promise.resolve(),Ma=()=>Aa||(ja.then(()=>Aa=0),Aa=Date.now());function Na(e,t){let n=e=>{if(!e._vts)e._vts=Date.now();else if(e._vts<=n.attached)return;tn(Pa(e,n.value),t,5,[e])};return n.value=e,n.attached=Ma(),n}function Pa(e,t){if(d(t)){let n=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{n.call(e),e._stopped=!0},t.map(e=>t=>!t._stopped&&e&&e(t))}else return t}const Fa=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,Ia=(e,t,n,r,i,s)=>{let c=i===`svg`;t===`class`?ua(e,r,c):t===`style`?ha(e,n,r):a(t)?o(t)||Da(e,t,n,r,s):(t[0]===`.`?(t=t.slice(1),!0):t[0]===`^`?(t=t.slice(1),!1):La(e,t,r,c))?(Ca(e,t,r),!e.tagName.includes(`-`)&&(t===`value`||t===`checked`||t===`selected`)&&Sa(e,t,r,c,s,t!==`value`)):e._isVueCE&&(/[A-Z]/.test(t)||!h(r))?Ca(e,T(t),r,s,t):(t===`true-value`?e._trueValue=r:t===`false-value`&&(e._falseValue=r),Sa(e,t,r,c))};function La(e,t,n,r){if(r)return!!(t===`innerHTML`||t===`textContent`||t in e&&Fa(t)&&m(n));if(t===`spellcheck`||t===`draggable`||t===`translate`||t===`autocorrect`||t===`form`||t===`list`&&e.tagName===`INPUT`||t===`type`&&e.tagName===`TEXTAREA`)return!1;if(t===`width`||t===`height`){let t=e.tagName;if(t===`IMG`||t===`VIDEO`||t===`CANVAS`||t===`SOURCE`)return!1}return Fa(t)&&h(n)?!1:t in e}Symbol(`_moveCb`),Symbol(`_enterCb`),Symbol(`_assign`);const Ra=s({patchProp:Ia},ca);let za;function Ba(){return za||=zr(Ra)}const Va=(...e)=>{let t=Ba().createApp(...e),{mount:n}=t;return t.mount=e=>{let r=Ua(e);if(!r)return;let i=t._component;!m(i)&&!i.render&&!i.template&&(i.template=r.innerHTML),r.nodeType===1&&(r.textContent=``);let a=n(r,!1,Ha(r));return r instanceof Element&&(r.removeAttribute(`v-cloak`),r.setAttribute(`data-v-app`,``)),a},t};function Ha(e){if(e instanceof SVGElement)return`svg`;if(typeof MathMLElement==`function`&&e instanceof MathMLElement)return`mathml`}function Ua(e){if(h(e)){let t=document.querySelector(e);return t}return e}let Wa;const Ga=e=>Wa=e,Ka=Symbol();var qa;(function(e){e.direct=`direct`,e.patchObject=`patch object`,e.patchFunction=`patch function`})(qa||={});const Ja=typeof window<`u`,Ya=(()=>typeof window==`object`&&window.window===window?window:typeof self==`object`&&self.self===self?self:typeof global==`object`&&global.global===global?global:typeof globalThis==`object`?globalThis:{HTMLElement:null})();function Xa(e,{autoBom:t=!1}={}){return t&&/^\s*(?:text\/\S*|application\/xml|\S*\/\S*\+xml)\s*;.*charset\s*=\s*utf-8/i.test(e.type)?new Blob([`﻿`,e],{type:e.type}):e}function Za(e,t,n){let r=new XMLHttpRequest;r.open(`GET`,e),r.responseType=`blob`,r.onload=function(){no(r.response,t,n)},r.onerror=function(){console.error(`could not download file`)},r.send()}function Qa(e){let t=new XMLHttpRequest;t.open(`HEAD`,e,!1);try{t.send()}catch{}return t.status>=200&&t.status<=299}function $a(e){try{e.dispatchEvent(new MouseEvent(`click`))}catch{let t=new MouseEvent(`click`,{bubbles:!0,cancelable:!0,view:window,detail:0,screenX:80,screenY:20,clientX:80,clientY:20,ctrlKey:!1,altKey:!1,shiftKey:!1,metaKey:!1,button:0,relatedTarget:null});e.dispatchEvent(t)}}const eo=typeof navigator==`object`?navigator:{userAgent:``},to=(()=>/Macintosh/.test(eo.userAgent)&&/AppleWebKit/.test(eo.userAgent)&&!/Safari/.test(eo.userAgent))(),no=Ja?typeof HTMLAnchorElement<`u`&&`download`in HTMLAnchorElement.prototype&&!to?ro:`msSaveOrOpenBlob`in eo?io:ao:()=>{};function ro(e,t=`download`,n){let r=document.createElement(`a`);r.download=t,r.rel=`noopener`,typeof e==`string`?(r.href=e,r.origin===location.origin?$a(r):Qa(r.href)?Za(e,t,n):(r.target=`_blank`,$a(r))):(r.href=URL.createObjectURL(e),setTimeout(function(){URL.revokeObjectURL(r.href)},4e4),setTimeout(function(){$a(r)},0))}function io(e,t=`download`,n){if(typeof e==`string`)if(Qa(e))Za(e,t,n);else{let t=document.createElement(`a`);t.href=e,t.target=`_blank`,setTimeout(function(){$a(t)})}else navigator.msSaveOrOpenBlob(Xa(e,n),t)}function ao(e,t,n,r){if(r||=open(``,`_blank`),r&&(r.document.title=r.document.body.innerText=`downloading...`),typeof e==`string`)return Za(e,t,n);let i=e.type===`application/octet-stream`,a=/constructor/i.test(String(Ya.HTMLElement))||`safari`in Ya,o=/CriOS\/[\d]+/.test(navigator.userAgent);if((o||i&&a||to)&&typeof FileReader<`u`){let t=new FileReader;t.onloadend=function(){let e=t.result;if(typeof e!=`string`)throw r=null,Error(`Wrong reader.result type`);e=o?e:e.replace(/^data:[^;]*;/,`data:attachment/file;`),r?r.location.href=e:location.assign(e),r=null},t.readAsDataURL(e)}else{let t=URL.createObjectURL(e);r?r.location.assign(t):location.href=t,r=null,setTimeout(function(){URL.revokeObjectURL(t)},4e4)}}const{assign:oo}=Object;function so(){let e=Se(!0),t=e.run(()=>zt({})),n=[],r=[],i=Lt({install(e){Ga(i),i._a=e,e.provide(Ka,i),e.config.globalProperties.$pinia=i,r.forEach(e=>n.push(e)),r=[]},use(e){return this._a?n.push(e):r.push(e),this},_p:n,_a:null,_e:e,_s:new Map,state:t});return i}Symbol(),Symbol(),Symbol();const{assign:co}=Object;var lo=`data:image/svg+xml,%3csvg%20xmlns='http://www.w3.org/2000/svg'%20viewBox='0%200%20261.76%20226.69'%3e%3cpath%20d='M161.096.001l-30.225%2052.351L100.647.001H-.005l130.877%20226.688L261.749.001z'%20fill='%2341b883'/%3e%3cpath%20d='M161.096.001l-30.225%2052.351L100.647.001H52.346l78.526%20136.01L209.398.001z'%20fill='%2334495e'/%3e%3c/svg%3e`;const uo=typeof document<`u`;function fo(e){return typeof e==`object`||`displayName`in e||`props`in e||`__vccOpts`in e}function po(e){return e.__esModule||e[Symbol.toStringTag]===`Module`||e.default&&fo(e.default)}const Q=Object.assign;function mo(e,t){let n={};for(let r in t){let i=t[r];n[r]=$(i)?i.map(e):e(i)}return n}const ho=()=>{},$=Array.isArray,go=/#/g,_o=/&/g,vo=/\//g,yo=/=/g,bo=/\?/g,xo=/\+/g,So=/%5B/g,Co=/%5D/g,wo=/%5E/g,To=/%60/g,Eo=/%7B/g,Do=/%7C/g,Oo=/%7D/g,ko=/%20/g;function Ao(e){return encodeURI(``+e).replace(Do,`|`).replace(So,`[`).replace(Co,`]`)}function jo(e){return Ao(e).replace(Eo,`{`).replace(Oo,`}`).replace(wo,`^`)}function Mo(e){return Ao(e).replace(xo,`%2B`).replace(ko,`+`).replace(go,`%23`).replace(_o,`%26`).replace(To,"`").replace(Eo,`{`).replace(Oo,`}`).replace(wo,`^`)}function No(e){return Mo(e).replace(yo,`%3D`)}function Po(e){return Ao(e).replace(go,`%23`).replace(bo,`%3F`)}function Fo(e){return e==null?``:Po(e).replace(vo,`%2F`)}function Io(e){try{return decodeURIComponent(``+e)}catch{}return``+e}const Lo=/\/$/,Ro=e=>e.replace(Lo,``);function zo(e,t,n=`/`){let r,i={},a=``,o=``,s=t.indexOf(`#`),c=t.indexOf(`?`);return s<c&&s>=0&&(c=-1),c>-1&&(r=t.slice(0,c),a=t.slice(c+1,s>-1?s:t.length),i=e(a)),s>-1&&(r||=t.slice(0,s),o=t.slice(s,t.length)),r=qo(r??t,n),{fullPath:r+(a&&`?`)+a+o,path:r,query:i,hash:Io(o)}}function Bo(e,t){let n=t.query?e(t.query):``;return t.path+(n&&`?`)+n+(t.hash||``)}function Vo(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||`/`}function Ho(e,t,n){let r=t.matched.length-1,i=n.matched.length-1;return r>-1&&r===i&&Uo(t.matched[r],n.matched[i])&&Wo(t.params,n.params)&&e(t.query)===e(n.query)&&t.hash===n.hash}function Uo(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function Wo(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(let n in e)if(!Go(e[n],t[n]))return!1;return!0}function Go(e,t){return $(e)?Ko(e,t):$(t)?Ko(t,e):e===t}function Ko(e,t){return $(t)?e.length===t.length&&e.every((e,n)=>e===t[n]):e.length===1&&e[0]===t}function qo(e,t){if(e.startsWith(`/`))return e;if(!e)return t;let n=t.split(`/`),r=e.split(`/`),i=r[r.length-1];(i===`..`||i===`.`)&&r.push(``);let a=n.length-1,o,s;for(o=0;o<r.length;o++)if(s=r[o],s!==`.`)if(s===`..`)a>1&&a--;else break;return n.slice(0,a).join(`/`)+`/`+r.slice(o).join(`/`)}const Jo={path:`/`,name:void 0,params:{},query:{},hash:``,fullPath:`/`,matched:[],meta:{},redirectedFrom:void 0};var Yo;(function(e){e.pop=`pop`,e.push=`push`})(Yo||={});var Xo;(function(e){e.back=`back`,e.forward=`forward`,e.unknown=``})(Xo||={});function Zo(e){if(!e)if(uo){let t=document.querySelector(`base`);e=t&&t.getAttribute(`href`)||`/`,e=e.replace(/^\w+:\/\/[^\/]+/,``)}else e=`/`;return e[0]!==`/`&&e[0]!==`#`&&(e=`/`+e),Ro(e)}const Qo=/^[^#]+#/;function $o(e,t){return e.replace(Qo,`#`)+t}function es(e,t){let n=document.documentElement.getBoundingClientRect(),r=e.getBoundingClientRect();return{behavior:t.behavior,left:r.left-n.left-(t.left||0),top:r.top-n.top-(t.top||0)}}const ts=()=>({left:window.scrollX,top:window.scrollY});function ns(e){let t;if(`el`in e){let n=e.el,r=typeof n==`string`&&n.startsWith(`#`),i=typeof n==`string`?r?document.getElementById(n.slice(1)):document.querySelector(n):n;if(!i)return;t=es(i,e)}else t=e;`scrollBehavior`in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left==null?window.scrollX:t.left,t.top==null?window.scrollY:t.top)}function rs(e,t){let n=history.state?history.state.position-t:-1;return n+e}const os=new Map;function ss(e,t){os.set(e,t)}function cs(e){let t=os.get(e);return os.delete(e),t}let ls=()=>location.protocol+`//`+location.host;function us(e,t){let{pathname:n,search:r,hash:i}=t,a=e.indexOf(`#`);if(a>-1){let t=i.includes(e.slice(a))?e.slice(a).length:1,n=i.slice(t);return n[0]!==`/`&&(n=`/`+n),Vo(n,``)}let o=Vo(n,e);return o+r+i}function ds(e,t,n,r){let i=[],a=[],o=null,s=({state:a})=>{let s=us(e,location),c=n.value,l=t.value,u=0;if(a){if(n.value=s,t.value=a,o&&o===c){o=null;return}u=l?a.position-l.position:0}else r(s);i.forEach(e=>{e(n.value,c,{delta:u,type:Yo.pop,direction:u?u>0?Xo.forward:Xo.back:Xo.unknown})})};function c(){o=n.value}function l(e){i.push(e);let t=()=>{let t=i.indexOf(e);t>-1&&i.splice(t,1)};return a.push(t),t}function u(){let{history:e}=window;e.state&&e.replaceState(Q({},e.state,{scroll:ts()}),``)}function d(){for(let e of a)e();a=[],window.removeEventListener(`popstate`,s),window.removeEventListener(`beforeunload`,u)}return window.addEventListener(`popstate`,s),window.addEventListener(`beforeunload`,u,{passive:!0}),{pauseListeners:c,listen:l,destroy:d}}function fs(e,t,n,r=!1,i=!1){return{back:e,current:t,forward:n,replaced:r,position:window.history.length,scroll:i?ts():null}}function ps(e){let{history:t,location:n}=window,r={value:us(e,n)},i={value:t.state};i.value||a(r.value,{back:null,current:r.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function a(r,a,o){let s=e.indexOf(`#`),c=s>-1?(n.host&&document.querySelector(`base`)?e:e.slice(s))+r:ls()+e+r;try{t[o?`replaceState`:`pushState`](a,``,c),i.value=a}catch(e){console.error(e),n[o?`replace`:`assign`](c)}}function o(e,n){let o=Q({},t.state,fs(i.value.back,e,i.value.forward,!0),n,{position:i.value.position});a(e,o,!0),r.value=e}function s(e,n){let o=Q({},i.value,t.state,{forward:e,scroll:ts()});a(o.current,o,!0);let s=Q({},fs(r.value,e,null),{position:o.position+1},n);a(e,s,!1),r.value=e}return{location:r,state:i,push:s,replace:o}}function ms(e){e=Zo(e);let t=ps(e),n=ds(e,t.state,t.location,t.replace);function r(e,t=!0){t||n.pauseListeners(),history.go(e)}let i=Q({location:``,base:e,go:r,createHref:$o.bind(null,e)},t,n);return Object.defineProperty(i,`location`,{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(i,`state`,{enumerable:!0,get:()=>t.state.value}),i}function hs(e){return typeof e==`string`||e&&typeof e==`object`}function gs(e){return typeof e==`string`||typeof e==`symbol`}const _s=Symbol(``);var vs;(function(e){e[e.aborted=4]=`aborted`,e[e.cancelled=8]=`cancelled`,e[e.duplicated=16]=`duplicated`})(vs||={});function ys(e,t){return Q(Error(),{type:e,[_s]:!0},t)}function bs(e,t){return e instanceof Error&&_s in e&&(t==null||!!(e.type&t))}const xs=`[^/]+?`,Ss={sensitive:!1,strict:!1,start:!0,end:!0},Cs=/[.+*?^${}()[\]/\\]/g;function ws(e,t){let n=Q({},Ss,t),r=[],i=n.start?`^`:``,a=[];for(let t of e){let e=t.length?[]:[90];n.strict&&!t.length&&(i+=`/`);for(let r=0;r<t.length;r++){let o=t[r],s=40+(n.sensitive?.25:0);if(o.type===0)r||(i+=`/`),i+=o.value.replace(Cs,`\\$&`),s+=40;else if(o.type===1){let{value:e,repeatable:n,optional:c,regexp:l}=o;a.push({name:e,repeatable:n,optional:c});let u=l||xs;if(u!==xs){s+=10;try{`${u}`}catch(t){throw Error(`Invalid custom RegExp for param "${e}" (${u}): `+t.message)}}let d=n?`((?:${u})(?:/(?:${u}))*)`:`(${u})`;r||(d=c&&t.length<2?`(?:/${d})`:`/`+d),c&&(d+=`?`),i+=d,s+=20,c&&(s+=-8),n&&(s+=-20),u===`.*`&&(s+=-50)}e.push(s)}r.push(e)}if(n.strict&&n.end){let e=r.length-1;r[e][r[e].length-1]+=.7000000000000001}n.strict||(i+=`/?`),n.end?i+=`$`:n.strict&&!i.endsWith(`/`)&&(i+=`(?:/|$)`);let o=new RegExp(i,n.sensitive?``:`i`);function s(e){let t=e.match(o),n={};if(!t)return null;for(let e=1;e<t.length;e++){let r=t[e]||``,i=a[e-1];n[i.name]=r&&i.repeatable?r.split(`/`):r}return n}function c(t){let n=``,r=!1;for(let i of e){(!r||!n.endsWith(`/`))&&(n+=`/`),r=!1;for(let e of i)if(e.type===0)n+=e.value;else if(e.type===1){let{value:a,repeatable:o,optional:s}=e,c=a in t?t[a]:``;if($(c)&&!o)throw Error(`Provided param "${a}" is an array but it is not repeatable (* or + modifiers)`);let l=$(c)?c.join(`/`):c;if(!l)if(s)i.length<2&&(n.endsWith(`/`)?n=n.slice(0,-1):r=!0);else throw Error(`Missing required param "${a}"`);n+=l}}return n||`/`}return{re:o,score:r,keys:a,parse:s,stringify:c}}function Ts(e,t){let n=0;for(;n<e.length&&n<t.length;){let r=t[n]-e[n];if(r)return r;n++}return e.length<t.length?e.length===1&&e[0]===80?-1:1:e.length>t.length?t.length===1&&t[0]===80?1:-1:0}function Es(e,t){let n=0,r=e.score,i=t.score;for(;n<r.length&&n<i.length;){let e=Ts(r[n],i[n]);if(e)return e;n++}if(Math.abs(i.length-r.length)===1){if(Ds(r))return 1;if(Ds(i))return-1}return i.length-r.length}function Ds(e){let t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const Os={type:0,value:``},ks=/[a-zA-Z0-9_]/;function As(e){if(!e)return[[]];if(e===`/`)return[[Os]];if(!e.startsWith(`/`))throw Error(`Invalid path "${e}"`);function t(e){throw Error(`ERR (${n})/"${l}": ${e}`)}let n=0,r=n,i=[],a;function o(){a&&i.push(a),a=[]}let s=0,c,l=``,u=``;function d(){l&&(n===0?a.push({type:0,value:l}):n===1||n===2||n===3?(a.length>1&&(c===`*`||c===`+`)&&t(`A repeatable param (${l}) must be alone in its segment. eg: '/:ids+.`),a.push({type:1,value:l,regexp:u,repeatable:c===`*`||c===`+`,optional:c===`*`||c===`?`})):t(`Invalid state to consume buffer`),l=``)}function f(){l+=c}for(;s<e.length;){if(c=e[s++],c===`\\`&&n!==2){r=n,n=4;continue}switch(n){case 0:c===`/`?(l&&d(),o()):c===`:`?(d(),n=1):f();break;case 4:f(),n=r;break;case 1:c===`(`?n=2:ks.test(c)?f():(d(),n=0,c!==`*`&&c!==`?`&&c!==`+`&&s--);break;case 2:c===`)`?u[u.length-1]==`\\`?u=u.slice(0,-1)+c:n=3:u+=c;break;case 3:d(),n=0,c!==`*`&&c!==`?`&&c!==`+`&&s--,u=``;break;default:t(`Unknown state`);break}}return n===2&&t(`Unfinished custom RegExp for param "${l}"`),d(),o(),i}function js(e,t,n){let r=ws(As(e.path),n),i=Q(r,{record:e,parent:t,children:[],alias:[]});return t&&!i.record.aliasOf==!t.record.aliasOf&&t.children.push(i),i}function Ms(e,t){let n=[],r=new Map;t=Rs({strict:!1,end:!0,sensitive:!1},t);function i(e){return r.get(e)}function a(e,n,r){let i=!r,s=Ps(e);s.aliasOf=r&&r.record;let l=Rs(t,e),u=[s];if(`alias`in e){let t=typeof e.alias==`string`?[e.alias]:e.alias;for(let e of t)u.push(Ps(Q({},s,{components:r?r.record.components:s.components,path:e,aliasOf:r?r.record:s})))}let d,f;for(let t of u){let{path:u}=t;if(n&&u[0]!==`/`){let e=n.record.path,r=e[e.length-1]===`/`?``:`/`;t.path=n.record.path+(u&&r+u)}if(d=js(t,n,l),r?r.alias.push(d):(f||=d,f!==d&&f.alias.push(d),i&&e.name&&!Is(d)&&o(e.name)),Vs(d)&&c(d),s.children){let e=s.children;for(let t=0;t<e.length;t++)a(e[t],d,r&&r.children[t])}r||=d}return f?()=>{o(f)}:ho}function o(e){if(gs(e)){let t=r.get(e);t&&(r.delete(e),n.splice(n.indexOf(t),1),t.children.forEach(o),t.alias.forEach(o))}else{let t=n.indexOf(e);t>-1&&(n.splice(t,1),e.record.name&&r.delete(e.record.name),e.children.forEach(o),e.alias.forEach(o))}}function s(){return n}function c(e){let t=zs(e,n);n.splice(t,0,e),e.record.name&&!Is(e)&&r.set(e.record.name,e)}function l(e,t){let i,a={},o,s;if(`name`in e&&e.name){if(i=r.get(e.name),!i)throw ys(1,{location:e});s=i.record.name,a=Q(Ns(t.params,i.keys.filter(e=>!e.optional).concat(i.parent?i.parent.keys.filter(e=>e.optional):[]).map(e=>e.name)),e.params&&Ns(e.params,i.keys.map(e=>e.name))),o=i.stringify(a)}else if(e.path!=null)o=e.path,i=n.find(e=>e.re.test(o)),i&&(a=i.parse(o),s=i.record.name);else{if(i=t.name?r.get(t.name):n.find(e=>e.re.test(t.path)),!i)throw ys(1,{location:e,currentLocation:t});s=i.record.name,a=Q({},t.params,e.params),o=i.stringify(a)}let c=[],l=i;for(;l;)c.unshift(l.record),l=l.parent;return{name:s,path:o,params:a,matched:c,meta:Ls(c)}}e.forEach(e=>a(e));function u(){n.length=0,r.clear()}return{addRoute:a,resolve:l,removeRoute:o,clearRoutes:u,getRoutes:s,getRecordMatcher:i}}function Ns(e,t){let n={};for(let r of t)r in e&&(n[r]=e[r]);return n}function Ps(e){let t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:Fs(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:`components`in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,`mods`,{value:{}}),t}function Fs(e){let t={},n=e.props||!1;if(`component`in e)t.default=n;else for(let r in e.components)t[r]=typeof n==`object`?n[r]:n;return t}function Is(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function Ls(e){return e.reduce((e,t)=>Q(e,t.meta),{})}function Rs(e,t){let n={};for(let r in e)n[r]=r in t?t[r]:e[r];return n}function zs(e,t){let n=0,r=t.length;for(;n!==r;){let i=n+r>>1,a=Es(e,t[i]);a<0?r=i:n=i+1}let i=Bs(e);return i&&(r=t.lastIndexOf(i,r-1)),r}function Bs(e){let t=e;for(;t=t.parent;)if(Vs(t)&&Es(e,t)===0)return t}function Vs({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function Hs(e){let t={};if(e===``||e===`?`)return t;let n=e[0]===`?`,r=(n?e.slice(1):e).split(`&`);for(let e=0;e<r.length;++e){let n=r[e].replace(xo,` `),i=n.indexOf(`=`),a=Io(i<0?n:n.slice(0,i)),o=i<0?null:Io(n.slice(i+1));if(a in t){let e=t[a];$(e)||(e=t[a]=[e]),e.push(o)}else t[a]=o}return t}function Us(e){let t=``;for(let n in e){let r=e[n];if(n=No(n),r==null){r!==void 0&&(t+=(t.length?`&`:``)+n);continue}let i=$(r)?r.map(e=>e&&Mo(e)):[r&&Mo(r)];i.forEach(e=>{e!==void 0&&(t+=(t.length?`&`:``)+n,e!=null&&(t+=`=`+e))})}return t}function Ws(e){let t={};for(let n in e){let r=e[n];r!==void 0&&(t[n]=$(r)?r.map(e=>e==null?null:``+e):r==null?r:``+r)}return t}const Gs=Symbol(``),Ks=Symbol(``),qs=Symbol(``),Js=Symbol(``),Ys=Symbol(``);function Xs(){let e=[];function t(t){return e.push(t),()=>{let n=e.indexOf(t);n>-1&&e.splice(n,1)}}function n(){e=[]}return{add:t,list:()=>e.slice(),reset:n}}function Zs(e,t,n,r,i,a=e=>e()){let o=r&&(r.enterCallbacks[i]=r.enterCallbacks[i]||[]);return()=>new Promise((s,c)=>{let l=e=>{e===!1?c(ys(4,{from:n,to:t})):e instanceof Error?c(e):hs(e)?c(ys(2,{from:t,to:e})):(o&&r.enterCallbacks[i]===o&&typeof e==`function`&&o.push(e),s())},u=a(()=>e.call(r&&r.instances[i],t,n,l)),d=Promise.resolve(u);e.length<3&&(d=d.then(l)),d.catch(e=>c(e))})}function Qs(e,t,n,r,i=e=>e()){let a=[];for(let o of e)for(let e in o.components){let s=o.components[e];if(t!==`beforeRouteEnter`&&!o.instances[e])continue;if(fo(s)){let c=s.__vccOpts||s,l=c[t];l&&a.push(Zs(l,n,r,o,e,i))}else{let c=s();a.push(()=>c.then(a=>{if(!a)throw Error(`Couldn't resolve component "${e}" at "${o.path}"`);let s=po(a)?a.default:a;o.mods[e]=a,o.components[e]=s;let c=s.__vccOpts||s,l=c[t];return l&&Zs(l,n,r,o,e,i)()}))}}return a}function $s(e){let t=br(qs),n=br(Js),r=Z(()=>{let n=Ut(e.to);return t.resolve(n)}),i=Z(()=>{let{matched:e}=r.value,{length:t}=e,i=e[t-1],a=n.matched;if(!i||!a.length)return-1;let o=a.findIndex(Uo.bind(null,i));if(o>-1)return o;let s=ac(e[t-2]);return t>1&&ac(i)===s&&a[a.length-1].path!==s?a.findIndex(Uo.bind(null,e[t-2])):o}),a=Z(()=>i.value>-1&&ic(n.params,r.value.params)),o=Z(()=>i.value>-1&&i.value===n.matched.length-1&&Wo(n.params,r.value.params));function s(n={}){if(rc(n)){let n=t[Ut(e.replace)?`replace`:`push`](Ut(e.to)).catch(ho);return e.viewTransition&&typeof document<`u`&&`startViewTransition`in document&&document.startViewTransition(()=>n),n}return Promise.resolve()}return{route:r,href:Z(()=>r.value.href),isActive:a,isExactActive:o,navigate:s}}function ec(e){return e.length===1?e[0]:e}const tc=En({name:`RouterLink`,compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:`page`},viewTransition:Boolean},useLink:$s,setup(e,{slots:t}){let n=At($s(e)),{options:r}=br(qs),i=Z(()=>({[oc(e.activeClass,r.linkActiveClass,`router-link-active`)]:n.isActive,[oc(e.exactActiveClass,r.linkExactActiveClass,`router-link-exact-active`)]:n.isExactActive}));return()=>{let r=t.default&&ec(t.default(n));return e.custom?r:$i(`a`,{"aria-current":n.isExactActive?e.ariaCurrentValue:null,href:n.href,onClick:n.navigate,class:i.value},r)}}}),nc=tc;function rc(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){let t=e.currentTarget.getAttribute(`target`);if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function ic(e,t){for(let n in t){let r=t[n],i=e[n];if(typeof r==`string`){if(r!==i)return!1}else if(!$(i)||i.length!==r.length||r.some((e,t)=>e!==i[t]))return!1}return!0}function ac(e){return e?e.aliasOf?e.aliasOf.path:e.path:``}const oc=(e,t,n)=>e??t??n,sc=En({name:`RouterView`,inheritAttrs:!1,props:{name:{type:String,default:`default`},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:n}){let r=br(Ys),i=Z(()=>e.route||r.value),a=br(Ks,0),o=Z(()=>{let e=Ut(a),{matched:t}=i.value,n;for(;(n=t[e])&&!n.components;)e++;return e}),s=Z(()=>i.value.matched[o.value]);yr(Ks,Z(()=>o.value+1)),yr(Gs,s),yr(Ys,i);let c=zt();return Xr(()=>[c.value,s.value,e.name],([e,t,n],[r,i,a])=>{t&&(t.instances[n]=e,i&&i!==t&&e&&e===r&&(t.leaveGuards.size||(t.leaveGuards=i.leaveGuards),t.updateGuards.size||(t.updateGuards=i.updateGuards))),e&&t&&(!i||!Uo(t,i)||!r)&&(t.enterCallbacks[n]||[]).forEach(t=>t(e))},{flush:`post`}),()=>{let r=i.value,a=e.name,o=s.value,l=o&&o.components[a];if(!l)return cc(n.default,{Component:l,route:r});let u=o.props[a],d=u?u===!0?r.params:typeof u==`function`?u(r):u:null,f=e=>{e.component.isUnmounted&&(o.instances[a]=null)},p=$i(l,Q({},d,t,{onVnodeUnmounted:f,ref:c}));return cc(n.default,{Component:p,route:r})||p}}});function cc(e,t){if(!e)return null;let n=e(t);return n.length===1?n[0]:n}const lc=sc;function uc(e){let t=Ms(e.routes,e),n=e.parseQuery||Hs,r=e.stringifyQuery||Us,i=e.history,a=Xs(),o=Xs(),s=Xs(),c=Bt(Jo),l=Jo;uo&&e.scrollBehavior&&`scrollRestoration`in history&&(history.scrollRestoration=`manual`);let u=mo.bind(null,e=>``+e),d=mo.bind(null,Fo),f=mo.bind(null,Io);function p(e,n){let r,i;return gs(e)?(r=t.getRecordMatcher(e),i=n):i=e,t.addRoute(i,r)}function m(e){let n=t.getRecordMatcher(e);n&&t.removeRoute(n)}function h(){return t.getRoutes().map(e=>e.record)}function g(e){return!!t.getRecordMatcher(e)}function _(e,a){if(a=Q({},a||c.value),typeof e==`string`){let r=zo(n,e,a.path),o=t.resolve({path:r.path},a),s=i.createHref(r.fullPath);return Q(r,o,{params:f(o.params),hash:Io(r.hash),redirectedFrom:void 0,href:s})}let o;if(e.path!=null)o=Q({},e,{path:zo(n,e.path,a.path).path});else{let t=Q({},e.params);for(let e in t)t[e]??delete t[e];o=Q({},e,{params:d(t)}),a.params=d(a.params)}let s=t.resolve(o,a),l=e.hash||``;s.params=u(f(s.params));let p=Bo(r,Q({},e,{hash:jo(l),path:s.path})),m=i.createHref(p);return Q({fullPath:p,hash:l,query:r===Us?Ws(e.query):e.query||{}},s,{redirectedFrom:void 0,href:m})}function v(e){return typeof e==`string`?zo(n,e,c.value.path):Q({},e)}function y(e,t){if(l!==e)return ys(8,{from:t,to:e})}function b(e){return C(e)}function x(e){return b(Q(v(e),{replace:!0}))}function S(e){let t=e.matched[e.matched.length-1];if(t&&t.redirect){let{redirect:n}=t,r=typeof n==`function`?n(e):n;return typeof r==`string`&&(r=r.includes(`?`)||r.includes(`#`)?r=v(r):{path:r},r.params={}),Q({query:e.query,hash:e.hash,params:r.path==null?e.params:{}},r)}}function C(e,t){let n=l=_(e),i=c.value,a=e.state,o=e.force,s=e.replace===!0,u=S(n);if(u)return C(Q(v(u),{state:typeof u==`object`?Q({},a,u.state):a,force:o,replace:s}),t||n);let d=n;d.redirectedFrom=t;let f;return!o&&Ho(r,i,n)&&(f=ys(16,{to:d,from:i}),se(i,i,!0,!1)),(f?Promise.resolve(f):te(d,i)).catch(e=>bs(e)?bs(e,2)?e:k(e):ae(e,d,i)).then(e=>{if(e){if(bs(e,2))return C(Q({replace:s},v(e.to),{state:typeof e.to==`object`?Q({},a,e.to.state):a,force:o}),t||d)}else e=ne(d,i,!0,s,a);return T(d,i,e),e})}function w(e,t){let n=y(e,t);return n?Promise.reject(n):Promise.resolve()}function ee(e){let t=ue.values().next().value;return t&&typeof t.runWithContext==`function`?t.runWithContext(e):e()}function te(e,t){let n,[r,i,s]=dc(e,t);n=Qs(r.reverse(),`beforeRouteLeave`,e,t);for(let i of r)i.leaveGuards.forEach(r=>{n.push(Zs(r,e,t))});let c=w.bind(null,e,t);return n.push(c),fe(n).then(()=>{n=[];for(let r of a.list())n.push(Zs(r,e,t));return n.push(c),fe(n)}).then(()=>{n=Qs(i,`beforeRouteUpdate`,e,t);for(let r of i)r.updateGuards.forEach(r=>{n.push(Zs(r,e,t))});return n.push(c),fe(n)}).then(()=>{n=[];for(let r of s)if(r.beforeEnter)if($(r.beforeEnter))for(let i of r.beforeEnter)n.push(Zs(i,e,t));else n.push(Zs(r.beforeEnter,e,t));return n.push(c),fe(n)}).then(()=>(e.matched.forEach(e=>e.enterCallbacks={}),n=Qs(s,`beforeRouteEnter`,e,t,ee),n.push(c),fe(n))).then(()=>{n=[];for(let r of o.list())n.push(Zs(r,e,t));return n.push(c),fe(n)}).catch(e=>bs(e,8)?e:Promise.reject(e))}function T(e,t,n){s.list().forEach(r=>ee(()=>r(e,t,n)))}function ne(e,t,n,r,a){let o=y(e,t);if(o)return o;let s=t===Jo,l=uo?history.state:{};n&&(r||s?i.replace(e.fullPath,Q({scroll:s&&l&&l.scroll},a)):i.push(e.fullPath,a)),c.value=e,se(e,t,n,s),k()}let E;function re(){E||=i.listen((e,t,n)=>{if(!de.listening)return;let r=_(e),a=S(r);if(a){C(Q(a,{replace:!0,force:!0}),r).catch(ho);return}l=r;let o=c.value;uo&&ss(rs(o.fullPath,n.delta),ts()),te(r,o).catch(e=>bs(e,12)?e:bs(e,2)?(C(Q(v(e.to),{force:!0}),r).then(e=>{bs(e,20)&&!n.delta&&n.type===Yo.pop&&i.go(-1,!1)}).catch(ho),Promise.reject()):(n.delta&&i.go(-n.delta,!1),ae(e,r,o))).then(e=>{e||=ne(r,o,!1),e&&(n.delta&&!bs(e,8)?i.go(-n.delta,!1):n.type===Yo.pop&&bs(e,20)&&i.go(-1,!1)),T(r,o,e)}).catch(ho)})}let D=Xs(),O=Xs(),ie;function ae(e,t,n){k(e);let r=O.list();return r.length?r.forEach(r=>r(e,t,n)):console.error(e),Promise.reject(e)}function oe(){return ie&&c.value!==Jo?Promise.resolve():new Promise((e,t)=>{D.add([e,t])})}function k(e){return ie||(ie=!e,re(),D.list().forEach(([t,n])=>e?n(e):t()),D.reset()),e}function se(t,n,r,i){let{scrollBehavior:a}=e;if(!uo||!a)return Promise.resolve();let o=!r&&cs(rs(t.fullPath,0))||(i||!r)&&history.state&&history.state.scroll||null;return dn().then(()=>a(t,n,o)).then(e=>e&&ns(e)).catch(e=>ae(e,t,n))}let ce=e=>i.go(e),le,ue=new Set,de={currentRoute:c,listening:!0,addRoute:p,removeRoute:m,clearRoutes:t.clearRoutes,hasRoute:g,getRoutes:h,resolve:_,options:e,push:b,replace:x,go:ce,back:()=>ce(-1),forward:()=>ce(1),beforeEach:a.add,beforeResolve:o.add,afterEach:s.add,onError:O.add,isReady:oe,install(e){let t=this;e.component(`RouterLink`,nc),e.component(`RouterView`,lc),e.config.globalProperties.$router=t,Object.defineProperty(e.config.globalProperties,`$route`,{enumerable:!0,get:()=>Ut(c)}),uo&&!le&&c.value===Jo&&(le=!0,b(i.location).catch(e=>{}));let n={};for(let e in Jo)Object.defineProperty(n,e,{get:()=>c.value[e],enumerable:!0});e.provide(qs,t),e.provide(Js,jt(n)),e.provide(Ys,c);let r=e.unmount;ue.add(e),e.unmount=function(){ue.delete(e),ue.size<1&&(l=Jo,E&&E(),E=null,c.value=Jo,le=!1,ie=!1),r()}}};function fe(e){return e.reduce((e,t)=>e.then(()=>ee(t)),Promise.resolve())}return de}function dc(e,t){let n=[],r=[],i=[],a=Math.max(t.matched.length,e.matched.length);for(let o=0;o<a;o++){let a=t.matched[o];a&&(e.matched.find(e=>Uo(e,a))?r.push(a):n.push(a));let s=e.matched[o];s&&(t.matched.find(e=>Uo(e,s))||i.push(s))}return[n,r,i]}const fc={class:`greetings`},pc={class:`green`};var mc=En({__name:`HelloWorld`,props:{msg:{}},setup(e){return(e,t)=>(G(),K(`div`,fc,[q(`h1`,pc,ve(e.msg),1),t[0]||=q(`h3`,null,[Y(` You’ve successfully created a project with `),q(`a`,{href:`https://vite.dev/`,target:`_blank`,rel:`noopener`},`Vite`),Y(` + `),q(`a`,{href:`https://vuejs.org/`,target:`_blank`,rel:`noopener`},`Vue 3`),Y(`. What's next? `)],-1)]))}}),hc=(e,t)=>{let n=e.__vccOpts||e;for(let[e,r]of t)n[e]=r;return n},gc=hc(mc,[[`__scopeId`,`data-v-d1bb330e`]]);const _c={class:`wrapper`};var vc=En({__name:`App`,setup(e){return(e,t)=>(G(),K(U,null,[q(`header`,null,[t[2]||=q(`img`,{alt:`Vue logo`,class:`logo`,src:lo,width:`125`,height:`125`},null,-1),q(`div`,_c,[J(gc,{msg:`You did it!`}),q(`nav`,null,[J(Ut(nc),{to:`/`},{default:B(()=>t[0]||=[Y(`Home`,-1)]),_:1,__:[0]}),J(Ut(nc),{to:`/about`},{default:B(()=>t[1]||=[Y(`About`,-1)]),_:1,__:[1]})])])]),J(Ut(lc))],64))}}),yc=hc(vc,[[`__scopeId`,`data-v-85852c48`]]);const bc=`modulepreload`,xc=function(e){return`/`+e},Sc={},Cc=function(e,t,n){let r=Promise.resolve();if(t&&t.length>0){let e=document.getElementsByTagName(`link`),i=document.querySelector(`meta[property=csp-nonce]`),a=i?.nonce||i?.getAttribute(`nonce`);function o(e){return Promise.all(e.map(e=>Promise.resolve(e).then(e=>({status:`fulfilled`,value:e}),e=>({status:`rejected`,reason:e}))))}r=o(t.map(t=>{if(t=xc(t,n),t in Sc)return;Sc[t]=!0;let r=t.endsWith(`.css`),i=r?`[rel="stylesheet"]`:``,o=!!n;if(o)for(let n=e.length-1;n>=0;n--){let i=e[n];if(i.href===t&&(!r||i.rel===`stylesheet`))return}else if(document.querySelector(`link[href="${t}"]${i}`))return;let s=document.createElement(`link`);if(s.rel=r?`stylesheet`:bc,r||(s.as=`script`),s.crossOrigin=``,s.href=t,a&&s.setAttribute(`nonce`,a),document.head.appendChild(s),r)return new Promise((e,n)=>{s.addEventListener(`load`,e),s.addEventListener(`error`,()=>n(Error(`Unable to preload CSS for ${t}`)))})}))}function i(e){let t=new Event(`vite:preloadError`,{cancelable:!0});if(t.payload=e,window.dispatchEvent(t),!t.defaultPrevented)throw e}return r.then(t=>{for(let e of t||[]){if(e.status!==`rejected`)continue;i(e.reason)}return e().catch(i)})},wc={},Tc={class:`item`},Ec={class:`details`};function Dc(e,t){return G(),K(`div`,Tc,[q(`i`,null,[Jn(e.$slots,`icon`,{},void 0,!0)]),q(`div`,Ec,[q(`h3`,null,[Jn(e.$slots,`heading`,{},void 0,!0)]),Jn(e.$slots,`default`,{},void 0,!0)])])}var Oc=hc(wc,[[`render`,Dc],[`__scopeId`,`data-v-fd0742eb`]]);const kc={},Ac={xmlns:`http://www.w3.org/2000/svg`,width:`20`,height:`17`,fill:`currentColor`};function jc(e,t){return G(),K(`svg`,Ac,t[0]||=[q(`path`,{d:`M11 2.253a1 1 0 1 0-2 0h2zm-2 13a1 1 0 1 0 2 0H9zm.447-12.167a1 1 0 1 0 1.107-1.666L9.447 3.086zM1 2.253L.447 1.42A1 1 0 0 0 0 2.253h1zm0 13H0a1 1 0 0 0 1.553.833L1 15.253zm8.447.833a1 1 0 1 0 1.107-1.666l-1.107 1.666zm0-14.666a1 1 0 1 0 1.107 1.666L9.447 1.42zM19 2.253h1a1 1 0 0 0-.447-.833L19 2.253zm0 13l-.553.833A1 1 0 0 0 20 15.253h-1zm-9.553-.833a1 1 0 1 0 1.107 1.666L9.447 14.42zM9 2.253v13h2v-13H9zm1.553-.833C9.203.523 7.42 0 5.5 0v2c1.572 0 2.961.431 3.947 1.086l1.107-1.666zM5.5 0C3.58 0 1.797.523.447 1.42l1.107 1.666C2.539 2.431 3.928 2 5.5 2V0zM0 2.253v13h2v-13H0zm1.553 13.833C2.539 15.431 3.928 15 5.5 15v-2c-1.92 0-3.703.523-5.053 1.42l1.107 1.666zM5.5 15c1.572 0 2.961.431 3.947 1.086l1.107-1.666C9.203 13.523 7.42 13 5.5 13v2zm5.053-11.914C11.539 2.431 12.928 2 14.5 2V0c-1.92 0-3.703.523-5.053 1.42l1.107 1.666zM14.5 2c1.573 0 2.961.431 3.947 1.086l1.107-1.666C18.203.523 16.421 0 14.5 0v2zm3.5.253v13h2v-13h-2zm1.553 12.167C18.203 13.523 16.421 13 14.5 13v2c1.573 0 2.961.431 3.947 1.086l1.107-1.666zM14.5 13c-1.92 0-3.703.523-5.053 1.42l1.107 1.666C11.539 15.431 12.928 15 14.5 15v-2z`},null,-1)])}var Mc=hc(kc,[[`render`,jc]]);const Nc={},Pc={xmlns:`http://www.w3.org/2000/svg`,"xmlns:xlink":`http://www.w3.org/1999/xlink`,"aria-hidden":`true`,role:`img`,class:`iconify iconify--mdi`,width:`24`,height:`24`,preserveAspectRatio:`xMidYMid meet`,viewBox:`0 0 24 24`};function Fc(e,t){return G(),K(`svg`,Pc,t[0]||=[q(`path`,{d:`M20 18v-4h-3v1h-2v-1H9v1H7v-1H4v4h16M6.33 8l-1.74 4H7v-1h2v1h6v-1h2v1h2.41l-1.74-4H6.33M9 5v1h6V5H9m12.84 7.61c.1.22.16.48.16.8V18c0 .53-.21 1-.6 1.41c-.4.4-.85.59-1.4.59H4c-.55 0-1-.19-1.4-.59C2.21 19 2 18.53 2 18v-4.59c0-.32.06-.58.16-.8L4.5 7.22C4.84 6.41 5.45 6 6.33 6H7V5c0-.55.18-1 .57-1.41C7.96 3.2 8.44 3 9 3h6c.56 0 1.04.2 1.43.59c.39.41.57.86.57 1.41v1h.67c.88 0 1.49.41 1.83 1.22l2.34 5.39z`,fill:`currentColor`},null,-1)])}var Ic=hc(Nc,[[`render`,Fc]]);const Lc={},Rc={xmlns:`http://www.w3.org/2000/svg`,width:`18`,height:`20`,fill:`currentColor`};function zc(e,t){return G(),K(`svg`,Rc,t[0]||=[q(`path`,{d:`M11.447 8.894a1 1 0 1 0-.894-1.789l.894 1.789zm-2.894-.789a1 1 0 1 0 .894 1.789l-.894-1.789zm0 1.789a1 1 0 1 0 .894-1.789l-.894 1.789zM7.447 7.106a1 1 0 1 0-.894 1.789l.894-1.789zM10 9a1 1 0 1 0-2 0h2zm-2 2.5a1 1 0 1 0 2 0H8zm9.447-5.606a1 1 0 1 0-.894-1.789l.894 1.789zm-2.894-.789a1 1 0 1 0 .894 1.789l-.894-1.789zm2 .789a1 1 0 1 0 .894-1.789l-.894 1.789zm-1.106-2.789a1 1 0 1 0-.894 1.789l.894-1.789zM18 5a1 1 0 1 0-2 0h2zm-2 2.5a1 1 0 1 0 2 0h-2zm-5.447-4.606a1 1 0 1 0 .894-1.789l-.894 1.789zM9 1l.447-.894a1 1 0 0 0-.894 0L9 1zm-2.447.106a1 1 0 1 0 .894 1.789l-.894-1.789zm-6 3a1 1 0 1 0 .894 1.789L.553 4.106zm2.894.789a1 1 0 1 0-.894-1.789l.894 1.789zm-2-.789a1 1 0 1 0-.894 1.789l.894-1.789zm1.106 2.789a1 1 0 1 0 .894-1.789l-.894 1.789zM2 5a1 1 0 1 0-2 0h2zM0 7.5a1 1 0 1 0 2 0H0zm8.553 12.394a1 1 0 1 0 .894-1.789l-.894 1.789zm-1.106-2.789a1 1 0 1 0-.894 1.789l.894-1.789zm1.106 1a1 1 0 1 0 .894 1.789l-.894-1.789zm2.894.789a1 1 0 1 0-.894-1.789l.894 1.789zM8 19a1 1 0 1 0 2 0H8zm2-2.5a1 1 0 1 0-2 0h2zm-7.447.394a1 1 0 1 0 .894-1.789l-.894 1.789zM1 15H0a1 1 0 0 0 .553.894L1 15zm1-2.5a1 1 0 1 0-2 0h2zm12.553 2.606a1 1 0 1 0 .894 1.789l-.894-1.789zM17 15l.447.894A1 1 0 0 0 18 15h-1zm1-2.5a1 1 0 1 0-2 0h2zm-7.447-5.394l-2 1 .894 1.789 2-1-.894-1.789zm-1.106 1l-2-1-.894 1.789 2 1 .894-1.789zM8 9v2.5h2V9H8zm8.553-4.894l-2 1 .894 1.789 2-1-.894-1.789zm.894 0l-2-1-.894 1.789 2 1 .894-1.789zM16 5v2.5h2V5h-2zm-4.553-3.894l-2-1-.894 1.789 2 1 .894-1.789zm-2.894-1l-2 1 .894 1.789 2-1L8.553.106zM1.447 5.894l2-1-.894-1.789-2 1 .894 1.789zm-.894 0l2 1 .894-1.789-2-1-.894 1.789zM0 5v2.5h2V5H0zm9.447 13.106l-2-1-.894 1.789 2 1 .894-1.789zm0 1.789l2-1-.894-1.789-2 1 .894 1.789zM10 19v-2.5H8V19h2zm-6.553-3.894l-2-1-.894 1.789 2 1 .894-1.789zM2 15v-2.5H0V15h2zm13.447 1.894l2-1-.894-1.789-2 1 .894 1.789zM18 15v-2.5h-2V15h2z`},null,-1)])}var Bc=hc(Lc,[[`render`,zc]]);const Vc={},Hc={xmlns:`http://www.w3.org/2000/svg`,width:`20`,height:`20`,fill:`currentColor`};function Uc(e,t){return G(),K(`svg`,Hc,t[0]||=[q(`path`,{d:`M15 4a1 1 0 1 0 0 2V4zm0 11v-1a1 1 0 0 0-1 1h1zm0 4l-.707.707A1 1 0 0 0 16 19h-1zm-4-4l.707-.707A1 1 0 0 0 11 14v1zm-4.707-1.293a1 1 0 0 0-1.414 1.414l1.414-1.414zm-.707.707l-.707-.707.707.707zM9 11v-1a1 1 0 0 0-.707.293L9 11zm-4 0h1a1 1 0 0 0-1-1v1zm0 4H4a1 1 0 0 0 1.707.707L5 15zm10-9h2V4h-2v2zm2 0a1 1 0 0 1 1 1h2a3 3 0 0 0-3-3v2zm1 1v6h2V7h-2zm0 6a1 1 0 0 1-1 1v2a3 3 0 0 0 3-3h-2zm-1 1h-2v2h2v-2zm-3 1v4h2v-4h-2zm1.707 3.293l-4-4-1.414 1.414 4 4 1.414-1.414zM11 14H7v2h4v-2zm-4 0c-.276 0-.525-.111-.707-.293l-1.414 1.414C5.42 15.663 6.172 16 7 16v-2zm-.707 1.121l3.414-3.414-1.414-1.414-3.414 3.414 1.414 1.414zM9 12h4v-2H9v2zm4 0a3 3 0 0 0 3-3h-2a1 1 0 0 1-1 1v2zm3-3V3h-2v6h2zm0-6a3 3 0 0 0-3-3v2a1 1 0 0 1 1 1h2zm-3-3H3v2h10V0zM3 0a3 3 0 0 0-3 3h2a1 1 0 0 1 1-1V0zM0 3v6h2V3H0zm0 6a3 3 0 0 0 3 3v-2a1 1 0 0 1-1-1H0zm3 3h2v-2H3v2zm1-1v4h2v-4H4zm1.707 4.707l.586-.586-1.414-1.414-.586.586 1.414 1.414z`},null,-1)])}var Wc=hc(Vc,[[`render`,Uc]]);const Gc={},Kc={xmlns:`http://www.w3.org/2000/svg`,width:`20`,height:`20`,fill:`currentColor`};function qc(e,t){return G(),K(`svg`,Kc,t[0]||=[q(`path`,{d:`M10 3.22l-.61-.6a5.5 5.5 0 0 0-7.666.105 5.5 5.5 0 0 0-.114 7.665L10 18.78l8.39-8.4a5.5 5.5 0 0 0-.114-7.665 5.5 5.5 0 0 0-7.666-.105l-.61.61z`},null,-1)])}var Jc=hc(Gc,[[`render`,qc]]),Yc=En({__name:`TheWelcome`,setup(e){let t=()=>fetch(`/__open-in-editor?file=README.md`);return(e,n)=>(G(),K(U,null,[J(Oc,null,{icon:B(()=>[J(Mc)]),heading:B(()=>n[0]||=[Y(`Documentation`,-1)]),default:B(()=>[n[1]||=Y(` Vue’s `,-1),n[2]||=q(`a`,{href:`https://vuejs.org/`,target:`_blank`,rel:`noopener`},`official documentation`,-1),n[3]||=Y(` provides you with all information you need to get started. `,-1)]),_:1,__:[1,2,3]}),J(Oc,null,{icon:B(()=>[J(Ic)]),heading:B(()=>n[4]||=[Y(`Tooling`,-1)]),default:B(()=>[n[6]||=Y(` This project is served and bundled with `,-1),n[7]||=q(`a`,{href:`https://vite.dev/guide/features.html`,target:`_blank`,rel:`noopener`},`Vite`,-1),n[8]||=Y(`. The recommended IDE setup is `,-1),n[9]||=q(`a`,{href:`https://code.visualstudio.com/`,target:`_blank`,rel:`noopener`},`VSCode`,-1),n[10]||=Y(` + `,-1),n[11]||=q(`a`,{href:`https://github.com/vuejs/language-tools`,target:`_blank`,rel:`noopener`},`Vue - Official`,-1),n[12]||=Y(`. If you need to test your components and web pages, check out `,-1),n[13]||=q(`a`,{href:`https://vitest.dev/`,target:`_blank`,rel:`noopener`},`Vitest`,-1),n[14]||=Y(` and `,-1),n[15]||=q(`a`,{href:`https://www.cypress.io/`,target:`_blank`,rel:`noopener`},`Cypress`,-1),n[16]||=Y(` / `,-1),n[17]||=q(`a`,{href:`https://playwright.dev/`,target:`_blank`,rel:`noopener`},`Playwright`,-1),n[18]||=Y(`. `,-1),n[19]||=q(`br`,null,null,-1),n[20]||=Y(` More instructions are available in `,-1),q(`a`,{href:`javascript:void(0)`,onClick:t},n[5]||=[q(`code`,null,`README.md`,-1)]),n[21]||=Y(`. `,-1)]),_:1,__:[6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21]}),J(Oc,null,{icon:B(()=>[J(Bc)]),heading:B(()=>n[22]||=[Y(`Ecosystem`,-1)]),default:B(()=>[n[23]||=Y(` Get official tools and libraries for your project: `,-1),n[24]||=q(`a`,{href:`https://pinia.vuejs.org/`,target:`_blank`,rel:`noopener`},`Pinia`,-1),n[25]||=Y(`, `,-1),n[26]||=q(`a`,{href:`https://router.vuejs.org/`,target:`_blank`,rel:`noopener`},`Vue Router`,-1),n[27]||=Y(`, `,-1),n[28]||=q(`a`,{href:`https://test-utils.vuejs.org/`,target:`_blank`,rel:`noopener`},`Vue Test Utils`,-1),n[29]||=Y(`, and `,-1),n[30]||=q(`a`,{href:`https://github.com/vuejs/devtools`,target:`_blank`,rel:`noopener`},`Vue Dev Tools`,-1),n[31]||=Y(`. If you need more resources, we suggest paying `,-1),n[32]||=q(`a`,{href:`https://github.com/vuejs/awesome-vue`,target:`_blank`,rel:`noopener`},`Awesome Vue`,-1),n[33]||=Y(` a visit. `,-1)]),_:1,__:[23,24,25,26,27,28,29,30,31,32,33]}),J(Oc,null,{icon:B(()=>[J(Wc)]),heading:B(()=>n[34]||=[Y(`Community`,-1)]),default:B(()=>[n[35]||=Y(` Got stuck? Ask your question on `,-1),n[36]||=q(`a`,{href:`https://chat.vuejs.org`,target:`_blank`,rel:`noopener`},`Vue Land`,-1),n[37]||=Y(` (our official Discord server), or `,-1),n[38]||=q(`a`,{href:`https://stackoverflow.com/questions/tagged/vue.js`,target:`_blank`,rel:`noopener`},`StackOverflow`,-1),n[39]||=Y(`. You should also follow the official `,-1),n[40]||=q(`a`,{href:`https://bsky.app/profile/vuejs.org`,target:`_blank`,rel:`noopener`},`@vuejs.org`,-1),n[41]||=Y(` Bluesky account or the `,-1),n[42]||=q(`a`,{href:`https://x.com/vuejs`,target:`_blank`,rel:`noopener`},`@vuejs`,-1),n[43]||=Y(` X account for latest news in the Vue world. `,-1)]),_:1,__:[35,36,37,38,39,40,41,42,43]}),J(Oc,null,{icon:B(()=>[J(Jc)]),heading:B(()=>n[44]||=[Y(`Support Vue`,-1)]),default:B(()=>[n[45]||=Y(` As an independent project, Vue relies on community backing for its sustainability. You can help us by `,-1),n[46]||=q(`a`,{href:`https://vuejs.org/sponsor/`,target:`_blank`,rel:`noopener`},`becoming a sponsor`,-1),n[47]||=Y(`. `,-1)]),_:1,__:[45,46,47]})],64))}}),Xc=Yc,Zc=En({__name:`HomeView`,setup(e){return(e,t)=>(G(),K(`main`,null,[J(Xc)]))}}),Qc=Zc;const $c=uc({history:ms(`/`),routes:[{path:`/`,name:`home`,component:Qc},{path:`/about`,name:`about`,component:()=>Cc(()=>import(`./AboutView-HcdbvEAf.js`),__vite__mapDeps([0,1]))}]});var el=$c;const tl=Va(yc);tl.use(so()),tl.use(el),tl.mount(`#app`);export{hc as b,q as c,K as d,G as e};