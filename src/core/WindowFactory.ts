import MenuBuilder from './menu'
import SystemInfo from 'systeminformation'
import { PROXY, ASSETS_PATH, WINDOW_ADAPTER, MAIN_WINDOW_SIZE } from './Configure'
import { register } from './Eventer'
import { TEST, DEBUG } from '../env'
import { app, BrowserWindow, ipcMain, systemPreferences } from 'electron'
import url from 'url'
import path from 'path'
import fs from 'fs-extra'
import bridge from './MessageBridge'
import { EventEmitter } from 'events'
import mime from 'mime-types'

// Extend BrowserWindow to allow custom property $$name$$
declare module 'electron' {
	interface BrowserWindow {
		$$name$$?: string
	}
}

// 全局活动窗口管理
let activeWindow: Electron.CrossProcessExports.BrowserWindow | null = null

export function getActiveWindow(): Electron.CrossProcessExports.BrowserWindow | null {
	return activeWindow
}

export function setActiveWindow(window: Electron.CrossProcessExports.BrowserWindow | null) {
	activeWindow = window
}

/**
 * 窗口工厂类 - 负责创建和管理应用窗口
 * 重构后的版本，保持对外接口不变，内部结构更清晰
 */
export default class WindowFactory {
	private readonly screenSize: { width: number; height: number }
	private readonly dirname: string
	private readonly errorPage: string = '404 Not Found'
	private isInitialized: boolean = false

	constructor(screenSize: { width: number; height: number }, dirname: string) {
		this.screenSize = screenSize
		this.dirname = dirname
		this.initializeProxyHandlers()
	}

	/**
	 * 初始化代理处理器
	 * 分离初始化逻辑，使构造函数更清晰
	 */
	private initializeProxyHandlers(): void {
		if (this.isInitialized) {
			return
		}

		// 注册主要的代理通道处理器
		this.registerMainProxyHandler()

		// 注册更新器代理通道处理器
		this.registerUpdaterProxyHandler()

		this.isInitialized = true
	}

	/**
	 * 注册主要的代理通道处理器
	 */
	private registerMainProxyHandler(): void {
		register('proxy-pass', ({ request, callback }: any) => {
			const location = this.parseUrl(request.url)

			// 处理根路径
			if (location.pathname === '/') {
				location.pathname = 'index.html'
			}

			// 处理特殊的 __root__ 路径
			this.handleRootPath(location)

			// 构建文件路径并处理请求
			const filePath = path.join(ASSETS_PATH, location.host, location.pathname)
			this.handleFileRequest(filePath, location.pathname!, callback)
		})
	}

	/**
	 * 注册更新器代理通道处理器
	 */
	private registerUpdaterProxyHandler(): void {
		register('proxy-pass-updater', ({ request, callback }: any) => {
			console.log('on proxy-pass-updater', request)
			const location = this.parseUrl(request.url)

			// 处理根路径
			if (location.pathname === '/' || !location.pathname) {
				location.pathname = 'index.html'
			}

			// 构建文件路径并处理请求
			const filePath = path.join(this.dirname, location.host, location.pathname)
			this.handleFileRequest(filePath, location.pathname, callback)
		})
	}

	/**
	 * 解析URL
	 */
	private parseUrl(requestUrl: string): url.UrlWithStringQuery {
		const location = url.parse(requestUrl)
		location.pathname = location.pathname || ''
		location.host = location.host || ''
		return location
	}

	/**
	 * 处理特殊的 __root__ 路径
	 */
	private handleRootPath(location: url.UrlWithStringQuery): void {
		if (/^\/__root__/.test(location.pathname!)) {
			const parsed = location.pathname!.match(/^\/__root__\/([^/]+?)\/(.+)/)
			if (parsed) {
				location.host = parsed[1]
				location.pathname = parsed[2]
			}
		}
	}

	/**
	 * 处理文件请求
	 */
	private handleFileRequest(filePath: string, pathname: string, callback: (result: { mimeType: any; data: Buffer }) => void): void {
		const exist = fs.existsSync(filePath)
		const mimeType = mime.lookup(pathname)

		if (exist) {
			try {
				const data = fs.readFileSync(filePath)
				callback({ mimeType, data })
			} catch {
				callback({ mimeType, data: Buffer.from(this.errorPage) })
			}
		} else {
			callback({ mimeType, data: Buffer.from(this.errorPage) })
		}
	}

	/**
	 * 打开窗口 - 主要的公共接口方法
	 * 保持原有接口不变，内部重构为更清晰的结构
	 */
	open({ pack, delegate = {}, data = {}, unique = false, needSystemInfo = false }: any) {
		// 处理唯一窗口逻辑
		this.handleUniqueWindow(pack, unique)

		// 创建窗口事件发射器
		const winEventer = new EventEmitter()

		// 计算窗口尺寸
		const windowSize = this.calculateWindowSize(data)

		// 创建浏览器窗口
		const browserWindow = this.createBrowserWindow(windowSize, data)

		// 配置窗口
		this.configureWindow(browserWindow, pack, data)

		// 设置事件监听器
		this.setupWindowEventListeners(browserWindow, winEventer, delegate, data, needSystemInfo)

		// 设置菜单
		this.setupWindowMenu(browserWindow)

		// 更新全局活动窗口
		activeWindow = browserWindow

		// 返回窗口控制对象
		return this.createWindowController(browserWindow, winEventer, delegate)
	}

	/**
	 * 处理唯一窗口逻辑
	 */
	private handleUniqueWindow(pack: string, unique: boolean): void {
		if (unique) {
			BrowserWindow.getAllWindows().forEach((win: any) => {
				if (win.$$name$$ === pack) {
					win.close()
				}
			})
		}
	}

	/**
	 * 计算窗口尺寸
	 */
	private calculateWindowSize(data: any): { width: number; height: number } {
		const { width, height } = data.size || MAIN_WINDOW_SIZE
		console.log('🚀 ~ WindowFactory ~ width, height:', width, height)
		console.log('🚀 ~ WindowFactory ~ screenSize:', this.screenSize)
		console.log('🚀 ~ WindowFactory ~ data.ratio:', data.ratio)

		let ratio = Math.min(this.screenSize.width / width, this.screenSize.height / height)
		console.log('🚀 ~ WindowFactory ~ ratio:', ratio)

		ratio *= data.ratio || 1
		console.log('🚀 ~ WindowFactory ~ final ratio:', ratio)

		const finalWidth = (width * ratio) | 0
		const finalHeight = (height * ratio) | 0
		console.log('🚀 ~ WindowFactory ~ final size:', finalWidth, finalHeight)

		return { width: finalWidth, height: finalHeight }
	}

	/**
	 * 创建浏览器窗口
	 */
	private createBrowserWindow(windowSize: { width: number; height: number }, data: any): BrowserWindow {
		return new BrowserWindow({
			title: '豆神王者Club',
			width: windowSize.width,
			height: windowSize.height,
			resizable: true,
			center: true,
			frame: !data.noFrame,
			transparent: !!data.transparent,
			autoHideMenuBar: true,
			webPreferences: {
				webSecurity: false,
				nodeIntegration: true,
				contextIsolation: false
			}
		})
	}

	/**
	 * 配置窗口
	 */
	private configureWindow(window: BrowserWindow, pack: string, data: any): void {
		// 设置用户代理
		const userAgent = window.webContents.getUserAgent()
		window.webContents.setUserAgent(`${userAgent} KCPC v${app.getVersion()} ${pack}`)

		// 确定加载URL
		const loadUrl = this.determineLoadUrl(pack, data)
		window.loadURL(loadUrl)

		// 开发模式下打开开发者工具
		if (DEBUG || TEST) {
			window.webContents.openDevTools()
		}

		// 设置窗口标识
		;(window as any).$$name$$ = pack
	}

	/**
	 * 确定窗口加载的URL
	 */
	private determineLoadUrl(pack: string, data: any): string {
		if (data.remoteUrl) {
			return data.remoteUrl
		}

		if (DEBUG && WINDOW_ADAPTER[pack]) {
			console.log('Using window adapter URL:', WINDOW_ADAPTER[pack])
			return WINDOW_ADAPTER[pack]
		}

		return `${PROXY}://${pack}${TEST || DEBUG ? '?env=test' : ''}`
	}

	/**
	 * 设置窗口事件监听器
	 */
	private setupWindowEventListeners(
		window: BrowserWindow,
		winEventer: EventEmitter,
		delegate: any,
		data: any,
		needSystemInfo: boolean
	): void {
		console.log('DEBUG_LOG:app.getPath("userData")', app.getPath('userData'))

		// IPC消息监听
		window.webContents.on('ipc-message', (_event, message) => {
			winEventer.emit('ipc-message', message)
		})

		// 页面加载完成事件
		window.webContents.on('did-finish-load', () => {
			this.handleWindowLoaded(window, winEventer, data, needSystemInfo)
		})

		// 声网权限处理
		window.webContents.once('did-finish-load', async () => {
			this.setupMediaPermissionHandler()
		})

		// 窗口关闭事件
		window.on('closed', () => {
			this.handleWindowClosed(delegate, winEventer)
		})

		// 设置代理
		bridge.delegate = delegate

		// 延迟触发启动事件
		process.nextTick(() => {
			winEventer.emit('start')
		})
	}

	/**
	 * 处理窗口加载完成
	 */
	private handleWindowLoaded(window: BrowserWindow, winEventer: EventEmitter, data: any, needSystemInfo: boolean): void {
		// 发送配置信息
		window.webContents.send('configure', {
			__dirname: path.resolve(__dirname, '..'),
			__apppath: app.getAppPath(),
			version: app.getVersion(),
			data,
			TEST,
			DEBUG
		})

		// 发送系统信息（如果需要）
		if (needSystemInfo) {
			SystemInfo.getStaticData((info: any) => {
				window.webContents.send('systeminfo', {
					systeminfo: info
				})
			})
			console.log('send system info')
		}

		winEventer.emit('loaded')
	}

	/**
	 * 设置媒体权限处理器
	 */
	private setupMediaPermissionHandler(): void {
		ipcMain.handle('IPC_REQUEST_PERMISSION_HANDLER', async (_event, arg) => {
			if (systemPreferences.getMediaAccessStatus(arg.type) === 'not-determined') {
				console.log('main process request handler:' + JSON.stringify(arg))
				return await systemPreferences.askForMediaAccess(arg.type)
			}
		})
	}

	/**
	 * 处理窗口关闭
	 */
	private handleWindowClosed(delegate: any, winEventer: EventEmitter): void {
		for (const key in delegate) {
			console.log('remove delegate for key')
			bridge.removeDelegate(key)
		}
		winEventer.emit('closed')
	}

	/**
	 * 设置窗口菜单
	 */
	private setupWindowMenu(window: BrowserWindow): void {
		const menuBuilder = new MenuBuilder(window)
		menuBuilder.buildMenu()
	}

	/**
	 * 创建窗口控制器对象
	 */
	private createWindowController(window: BrowserWindow, winEventer: EventEmitter, delegate: any) {
		const sendMessage = (...args: [channel: string, ...args: any[]]) => {
			if (!window.isDestroyed()) {
				window.webContents.send(...args)
			}
		}

		const setDelegate = () => {
			bridge.delegate = delegate
		}

		// 初始设置代理
		setDelegate()

		return { winEventer, window, sendMessage, setDelegate }
	}
}
