import { contextBridge, ipc<PERSON><PERSON><PERSON> } from 'electron'
import { electronAPI } from '@electron-toolkit/preload'
import {
	MainProcessAPI,
	MainProcessEvents,
	RendererProcessEvents,
	IPCRequest,
	IPCResponse,
	IPCEvent,
	ElectronAPI
} from '../method.types'

/**
 * 创建类型安全的Electron API桥接
 */
function createElectronBridge(): ElectronAPI {
	// 事件监听器管理
	const eventListeners = new Map<string, Set<() => void>>()

	// 设置事件监听
	ipcRenderer.on('electron-event-broadcast', (_, eventData: IPCEvent) => {
		const listeners = eventListeners.get(eventData.event)
		if (listeners) {
			listeners.forEach((listener) => {
				try {
					listener(eventData.data)
				} catch (error) {
					console.error(`Error in event listener for ${eventData.event}:`, error)
				}
			})
		}
	})

	// 监听直接发送的download-event事件
	ipcRenderer.on('download-event', (_, eventData: any) => {
		const listeners = eventListeners.get('download-event')
		if (listeners) {
			listeners.forEach((listener) => {
				try {
					listener(eventData)
				} catch (error) {
					console.error(`Error in event listener for download-event:`, error)
				}
			})
		}
	})

	return {
		/**
		 * 调用主进程方法
		 */
		async invoke<K extends keyof MainProcessAPI>(
			method: K,
			...args: Parameters<MainProcessAPI[K]>
		): Promise<ReturnType<MainProcessAPI[K]>> {
			const request: IPCRequest = {
				id: `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
				method,
				args: args.length === 1 ? args[0] : args,
				timestamp: Date.now()
			}

			try {
				const response: IPCResponse = await ipcRenderer.invoke(
					'electron-api-invoke',
					request
				)

				if (!response.success) {
					const error = new Error(response.error?.message || 'Unknown error')
					if (response.error?.code) {
						;(error as any).code = response.error.code
					}
					if (response.error?.stack) {
						error.stack = response.error.stack
					}
					throw error
				}

				return response.data
			} catch (error: any) {
				console.error(`Error invoking ${method}:`, error)
				throw error
			}
		},

		/**
		 * 监听主进程事件
		 */
		on<K extends keyof MainProcessEvents>(
			event: K,
			listener: (data: MainProcessEvents[K]) => void
		): () => void {
			const eventName = event as string

			if (!eventListeners.has(eventName)) {
				eventListeners.set(eventName, new Set())
			}

			eventListeners.get(eventName)!.add(listener)

			// 返回取消监听的函数
			return () => {
				const listeners = eventListeners.get(eventName)
				if (listeners) {
					listeners.delete(listener)
					if (listeners.size === 0) {
						eventListeners.delete(eventName)
					}
				}
			}
		},

		/**
		 * 移除事件监听器
		 */
		off<K extends keyof MainProcessEvents>(
			event: K,
			listener?: (data: MainProcessEvents[K]) => void
		): void {
			const eventName = event as string
			const listeners = eventListeners.get(eventName)

			if (!listeners) return

			if (listener) {
				listeners.delete(listener)
				if (listeners.size === 0) {
					eventListeners.delete(eventName)
				}
			} else {
				// 如果没有指定监听器，移除所有监听器
				eventListeners.delete(eventName)
			}
		},

		/**
		 * 向主进程发送事件
		 */
		emit<K extends keyof RendererProcessEvents>(
			event: K,
			data: RendererProcessEvents[K]
		): void {
			const eventData: IPCEvent = {
				event: event as string,
				data,
				timestamp: Date.now()
			}

			ipcRenderer.send('electron-event-emit', eventData)
		},

		/**
		 * 一次性监听事件
		 */
		once<K extends keyof MainProcessEvents>(
			event: K,
			listener: (data: MainProcessEvents[K]) => void
		): void {
			const onceListener = (data: MainProcessEvents[K]) => {
				listener(data)
				this.off(event, onceListener)
			}

			this.on(event, onceListener)
		}
	}
}

// 创建API实例
const electronBridge = createElectronBridge()

// Custom APIs for renderer (保持向后兼容)
const api = {}

// Use `contextBridge` APIs to expose Electron APIs to
// renderer only if context isolation is enabled, otherwise
// just add to the DOM global.
if (process.contextIsolated) {
	try {
		contextBridge.exposeInMainWorld('electron', electronAPI)
		contextBridge.exposeInMainWorld('electronAPI', electronBridge)
		contextBridge.exposeInMainWorld('api', api)
	} catch (error) {
		console.error('Failed to expose APIs to main world:', error)
	}
} else {
	// @ts-ignore (define in dts)
	window.electron = electronAPI
	// @ts-ignore (define in dts)
	window.electronAPI = electronBridge
	// @ts-ignore (define in dts)
	window.api = api
}
