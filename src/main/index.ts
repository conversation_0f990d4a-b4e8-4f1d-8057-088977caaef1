import { app, shell, BrowserWindow, ipcMain } from 'electron'
import { join } from 'path'
import { electronApp, optimizer, is } from '@electron-toolkit/utils'
import icon from '../../resources/icon.png?inline'
import { electronMain } from '../core/electron-main'
import UpdaterWindow from '../core/UpdaterWindow'
import { WindowFactoryOptions, WindowSizeData } from '../method.types'

function createWindow(): void {
	// Create the browser window.
	const mainWindow = new BrowserWindow({
		width: 900,
		height: 670,
		show: false,
		autoHideMenuBar: true,
		...(process.platform === 'linux' ? { icon } : {}),
		webPreferences: {
			preload: join(__dirname, '../preload/index.js'),
			sandbox: false
		}
	})

	mainWindow.on('ready-to-show', () => {
		mainWindow.show()
	})

	mainWindow.webContents.setWindowOpenHandler((details) => {
		shell.openExternal(details.url)
		return { action: 'deny' }
	})

	// HMR for renderer base on electron-vite cli.
	// Load the remote URL for development or the local html file for production.
	if (is.dev && process.env['ELECTRON_RENDERER_URL']) {
		mainWindow.loadURL(process.env['ELECTRON_RENDERER_URL'])
	} else {
		mainWindow.loadFile(join(__dirname, '../renderer/index.html'))
	}
}

// This method will be called when Electron has finished
// initialization and is ready to create browser windows.
// Some APIs can only be used after this event occurs.
app.whenReady().then(() => {
	// Set app user model id for windows
	electronApp.setAppUserModelId('com.electron')

	// Default open or close DevTools by F12 in development
	// and ignore CommandOrControl + R in production.
	// see https://github.com/alex8088/electron-toolkit/tree/master/packages/utils
	app.on('browser-window-created', (_, window) => {
		optimizer.watchWindowShortcuts(window)
	})

	// IPC test (保持向后兼容)
	ipcMain.on('ping', () => console.log('pong'))

	// 初始化 Electron Server
	console.log('Electron Server initialized')

	// 监听渲染进程事件示例
	electronMain.onRendererEvent('user-action', (data) => {
		console.log('User action:', data)
	})

	electronMain.onRendererEvent('error-occurred', (data) => {
		console.error('Renderer error:', data)
	})

	// createWindow()

	const updater = new UpdaterWindow(__dirname)
	updater.on('open-main-window', (options: { pack: string; data: WindowSizeData }) => {
		console.log('Open main window:', options)
		setTimeout(() => {
			updater.close()
		}, 100)
		// TODO
		const mainWindowOption: WindowFactoryOptions = {
			pack: options.pack,
			data: options.data
		}

		electronMain.openMainWindow(mainWindowOption)
	})
	updater.start()

	// 应用准备就绪后广播事件
	electronMain.broadcast('app-ready', undefined)

	app.on('activate', function () {
		// On macOS it's common to re-create a window in the app when the
		// dock icon is clicked and there are no other windows open.
		if (BrowserWindow.getAllWindows().length === 0) createWindow()
	})
})

// Quit when all windows are closed, except on macOS. There, it's common
// for applications and their menu bar to stay active until the user quits
// explicitly with Cmd + Q.
app.on('window-all-closed', () => {
	// 广播应用即将退出事件
	electronMain.broadcast('app-will-quit', undefined)
	// if (process.platform !== 'darwin') {
	app.quit()
	// }
})

// In this file you can include the rest of your app's specific main process
// code. You can also put them in separate files and require them here.
