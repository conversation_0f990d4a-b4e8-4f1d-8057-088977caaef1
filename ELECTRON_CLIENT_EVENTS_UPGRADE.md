# ElectronClient 事件功能升级说明

## 概述

ElectronClient 现在支持完整的事件监听功能，通过内置 EventEmitter 实现本地事件系统，同时保持与现有 MainProcessEvents 的完全兼容性。

## 主要变化

### 1. 新增本地事件功能

ElectronClient 现在内置了 EventEmitter，可以发送和监听本地事件：

```typescript
// 监听本地事件
electronClient.addEventListener('my-event', (data) => {
  console.log('收到事件:', data)
})

// 发送本地事件
electronClient.emitLocal('my-event', { message: 'Hello!' })

// 一次性监听
electronClient.onceLocal('once-event', (data) => {
  console.log('只会收到一次:', data)
})
```

### 2. 主进程事件方法重命名

为了避免与 EventEmitter 方法冲突，主进程事件方法被重命名：

| 旧方法 | 新方法 | 说明 |
|--------|--------|------|
| `on()` | `onMainProcess()` | 监听主进程事件 |
| `once()` | `onceMainProcess()` | 一次性监听主进程事件 |
| `off()` | `offMainProcess()` | 移除主进程事件监听器 |
| `emit()` | `emitToMainProcess()` | 向主进程发送事件 |

### 3. 向后兼容性

所有旧的方法仍然可用，但被标记为 `@deprecated`：

```typescript
// 这些方法仍然可用，但建议使用新方法
electronClient.on('system-theme-changed', callback)  // 仍然工作
electronClient.onMainProcess('system-theme-changed', callback)  // 推荐使用
```

## 新增 API

### 本地事件方法

```typescript
// 监听本地事件
addEventListener<K extends keyof ElectronClientEvents>(
  event: K,
  listener: (...args: ElectronClientEvents[K]) => void
): this

// 移除本地事件监听器
removeEventListener<K extends keyof ElectronClientEvents>(
  event: K,
  listener: (...args: ElectronClientEvents[K]) => void
): this

// 一次性监听本地事件
onceLocal<K extends keyof ElectronClientEvents>(
  event: K,
  listener: (...args: ElectronClientEvents[K]) => void
): this

// 发送本地事件
emitLocal<K extends keyof ElectronClientEvents>(
  event: K,
  ...args: ElectronClientEvents[K]
): boolean

// 获取事件监听器数量
listenerCount(event: string): number

// 获取所有事件名称
eventNames(): (string | symbol)[]
```

### 主进程事件方法（新命名）

```typescript
// 监听主进程事件
onMainProcess<K extends keyof MainProcessEvents>(
  event: K,
  listener: (data: MainProcessEvents[K]) => void
): () => void

// 一次性监听主进程事件
onceMainProcess<K extends keyof MainProcessEvents>(
  event: K,
  listener: (data: MainProcessEvents[K]) => void
): void

// 移除主进程事件监听器
offMainProcess<K extends keyof MainProcessEvents>(
  event: K,
  listener?: (data: MainProcessEvents[K]) => void
): void

// 向主进程发送事件
emitToMainProcess<K extends keyof RendererProcessEvents>(
  event: K,
  data: RendererProcessEvents[K]
): void
```

## 使用示例

### 下载任务事件处理

```typescript
// 监听下载任务的本地事件
const taskId = 'download-123'

electronClient.addEventListener(`complete-${taskId}`, () => {
  console.log('下载完成')
})

electronClient.addEventListener(`progress-${taskId}`, (progress) => {
  console.log('下载进度:', progress)
})

electronClient.addEventListener(`error-${taskId}`, (error) => {
  console.log('下载错误:', error)
})
```

### 主进程事件处理

```typescript
// 监听系统主题变化
const unsubscribe = electronClient.onMainProcess('system-theme-changed', (theme) => {
  console.log('主题变化为:', theme)
})

// 或使用便捷方法
const unsubscribeTheme = electronClient.onThemeChange((theme) => {
  console.log('主题变化为:', theme)
})

// 清理监听器
unsubscribe()
unsubscribeTheme()
```

### 混合使用

```typescript
// 监听主进程通知
electronClient.onMainProcess('notification', (notification) => {
  // 转发为本地事件
  electronClient.emitLocal('app-notification', notification)
})

// 监听本地通知事件
electronClient.addEventListener('app-notification', (notification) => {
  // 处理通知显示逻辑
  showNotificationUI(notification)
})
```

## 清理和内存管理

```typescript
// 清理所有事件监听器（主进程和本地事件）
electronClient.cleanup()

// 只清理特定的本地事件监听器
electronClient.removeEventListener('my-event', myListener)

// 检查事件监听器数量
console.log('监听器数量:', electronClient.listenerCount('my-event'))
```

## 迁移指南

### 立即迁移（推荐）

```typescript
// 旧代码
electronClient.on('system-theme-changed', callback)
electronClient.emit('user-action', data)

// 新代码
electronClient.onMainProcess('system-theme-changed', callback)
electronClient.emitToMainProcess('user-action', data)
```

### 渐进式迁移

由于保持了向后兼容性，你可以：

1. 继续使用现有代码（会有 deprecation 警告）
2. 逐步将关键部分迁移到新 API
3. 在合适的时候完全迁移

## 注意事项

1. **事件命名空间**：本地事件和主进程事件是分离的，同名事件不会冲突
2. **性能**：本地事件处理更快，因为不需要 IPC 通信
3. **清理**：记得在组件销毁时调用 `cleanup()` 或手动移除监听器
4. **类型安全**：新 API 提供了更好的 TypeScript 类型支持

## 测试

运行测试文件来验证功能：

```typescript
import { runAllTests } from './test-electron-client-events'
runAllTests()
```

这将测试所有新功能和兼容性。
