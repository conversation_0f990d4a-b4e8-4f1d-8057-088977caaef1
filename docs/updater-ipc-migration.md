# Updater IPC 通信迁移指南

## 概述

`Updater.ts` 已经从使用 `MessageBridge` 迁移到使用现代 Electron IPC 通信。这个文档说明了新的通信方式以及如何在更新器窗口中使用它。

## 主要变更

### 1. 移除的依赖
- ❌ `MessageBridge` - 旧的消息桥接系统
- ❌ `@electron/remote` - 已弃用的远程模块

### 2. 新增的功能
- ✅ 现代 IPC 通信
- ✅ 类型安全的 API 调用
- ✅ 自动清理 IPC 监听器
- ✅ 窗口级别的通道隔离

## 新的通信架构

### 主进程 (Updater.ts)

```typescript
// 设置 IPC 通信
private setupIPC(): void {
    if (!this.window) return

    // 监听来自更新器窗口的 openMainWindow 请求
    const handleOpenMainWindow = (_event: any, options: { pack: string; data: any }) => {
        // 发射事件给外部监听器（保持向后兼容）
        this.emit('open-main-window', options)
    }

    // 设置 IPC 监听器，使用窗口 ID 作为唯一标识
    const channelName = `updater-open-main-window-${this.window.id}`
    ipcMain.on(channelName, handleOpenMainWindow)

    // 存储监听器引用以便清理
    this.window.once('closed', () => {
        ipcMain.removeListener(channelName, handleOpenMainWindow)
    })

    // 向更新器窗口发送通道名称，让它知道如何与主进程通信
    this.window.webContents.once('did-finish-load', () => {
        if (this.window && !this.window.isDestroyed()) {
            this.window.webContents.send('updater-ipc-channel', channelName)
        }
    })
}
```

### 渲染进程 (更新器窗口)

更新器窗口现在需要使用以下方式与主进程通信：

```javascript
// 监听主进程发送的通道名称
let ipcChannelName = null;

window.electronAPI.on('updater-ipc-channel', (channelName) => {
    ipcChannelName = channelName;
    console.log('Received IPC channel:', channelName);
});

// 使用通道名称发送 openMainWindow 请求
function openMainWindow(pack, data) {
    if (ipcChannelName) {
        window.electron.ipcRenderer.send(ipcChannelName, { pack, data });
    } else {
        console.error('IPC channel not ready');
    }
}

// 或者使用现代 electronAPI 方式
async function openMainWindowModern(pack, data) {
    try {
        const windowId = await window.electronAPI.invoke('openMainWindow', {
            pack,
            data,
            unique: true
        });
        console.log('Opened window:', windowId);
    } catch (error) {
        console.error('Failed to open main window:', error);
    }
}
```

## 迁移步骤

### 对于更新器窗口开发者

1. **移除 MessageBridge 调用**
   ```javascript
   // 旧方式 ❌
   bridge.call({
       method: 'openMainWindow',
       args: { pack: 'main-ui', data: {} }
   });
   
   // 新方式 ✅
   window.electronAPI.invoke('openMainWindow', {
       pack: 'main-ui',
       data: {},
       unique: true
   });
   ```

2. **使用类型安全的 API**
   ```javascript
   // 推荐使用 electronAPI 进行所有主进程通信
   const electronClient = new ElectronClient();
   await electronClient.openMainWindow({
       pack: 'main-ui',
       data: { size: { width: 1200, height: 800 } }
   });
   ```

## 向后兼容性

- ✅ `Updater` 类的公共 API 保持不变
- ✅ `open-main-window` 事件仍然会被发射
- ✅ 外部监听器无需修改

## 优势

1. **更好的类型安全** - 使用 TypeScript 接口定义
2. **自动清理** - IPC 监听器在窗口关闭时自动清理
3. **更好的错误处理** - 统一的错误处理机制
4. **现代化架构** - 符合 Electron 最佳实践
5. **窗口隔离** - 每个更新器窗口有独立的通信通道

## 示例代码

完整的更新器窗口示例：

```html
<!DOCTYPE html>
<html>
<head>
    <title>Updater</title>
</head>
<body>
    <button id="openMainBtn">打开主窗口</button>
    
    <script>
        // 等待 IPC 通道准备就绪
        let ready = false;
        
        window.electronAPI.on('updater-ipc-channel', (channelName) => {
            ready = true;
            console.log('Updater IPC ready');
        });
        
        document.getElementById('openMainBtn').addEventListener('click', async () => {
            if (!ready) {
                console.error('IPC not ready');
                return;
            }
            
            try {
                const windowId = await window.electronAPI.invoke('openMainWindow', {
                    pack: 'main-ui',
                    data: {
                        size: { width: 1200, height: 800 },
                        ratio: 0.9
                    },
                    unique: true
                });
                
                console.log('Successfully opened window:', windowId);
            } catch (error) {
                console.error('Failed to open main window:', error);
            }
        });
    </script>
</body>
</html>
```
