# Electron IPC 架构文档

## 📋 概述

这是一个简单明了的 Electron 函数调用和事件封装架构，提供类型安全的 IPC 通信机制。

## 🏗️ 架构设计

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   渲染进程       │    │   预加载脚本     │    │   主进程         │
│                │    │                │    │                │
│ ElectronClient  │◄──►│ ElectronBridge  │◄──►│ ElectronServer  │
│                │    │                │    │                │
│ - invoke()      │    │ - contextBridge │    │ - register()    │
│ - on()          │    │ - 类型安全      │    │ - emit()        │
│ - off()         │    │                │    │ - handle()      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🎯 核心特性

- ✅ **类型安全**: 完整的 TypeScript 类型定义
- ✅ **简单易用**: 统一的 API 接口，类似于普通的函数调用
- ✅ **事件驱动**: 支持双向事件通信
- ✅ **错误处理**: 统一的错误处理机制
- ✅ **可扩展**: 易于添加新的 API 方法

## 📁 文件结构

```
src/
├── method.types.d.ts           # 类型定义
├── core/
│   └── ElectronServer.ts       # 主进程服务器
├── preload/
│   └── index.ts               # 预加载脚本
├── renderer/src/utils/
│   └── electronClient.ts      # 渲染进程客户端
└── main/
    └── index.ts              # 主进程入口
```

## 🚀 快速开始

### 1. 在渲染进程中使用

```typescript
import { electronClient } from '@/utils/electronClient'

// 调用主进程方法
const systemInfo = await electronClient.getSystemInfo()
console.log('系统信息:', systemInfo)

// 发送事件到主进程
electronClient.emitUserAction('button-click', { buttonId: 'save' })
```

### 2. 在主进程中扩展 API

```typescript
import { electronServer } from '../core/ElectronServer'

// 注册新的 API 方法
electronServer.registerAPI({
  customMethod: async (data: any) => {
    // 处理逻辑
    return { success: true, data }
  }
})

// 广播事件到所有窗口
electronServer.broadcast('custom-event', { message: 'Hello World' })

// 监听渲染进程事件
electronServer.onRendererEvent('user-action', (data) => {
  console.log('用户操作:', data)
})
```

## 📖 API 参考

### ElectronClient (渲染进程)

#### 方法调用

```typescript
// 系统信息
await electronClient.getSystemInfo()
await electronClient.getAppVersion()

// 窗口控制
await electronClient.minimizeWindow()
await electronClient.maximizeWindow()
await electronClient.closeWindow()

// 文件操作
const filePath = await electronClient.selectFile({
  title: '选择文件',
  filters: [{ name: 'Text Files', extensions: ['txt'] }]
})

const content = await electronClient.readFile(filePath)
await electronClient.writeFile(filePath, 'new content')

// 应用设置
const settings = await electronClient.getSettings()
await electronClient.updateSettings({ theme: 'dark' })

// HTTP 请求
const response = await electronClient.httpRequest({
  url: 'https://api.example.com/data',
  method: 'GET'
})
```

#### 事件处理

```typescript
// 监听事件
const unsubscribe = electronClient.on('notification', (data) => {
  console.log('收到通知:', data)
})

// 一次性监听
electronClient.once('app-ready', () => {
  console.log('应用已准备就绪')
})

// 移除监听器
electronClient.off('notification')
unsubscribe() // 或使用返回的取消函数

// 发送事件
electronClient.emit('user-action', {
  action: 'page-view',
  data: { page: '/dashboard' },
  timestamp: Date.now()
})
```

### ElectronServer (主进程)

#### API 注册

```typescript
import { electronServer } from '../core/ElectronServer'

electronServer.registerAPI({
  // 添加新的 API 方法
  getUserData: async (userId: string) => {
    // 获取用户数据的逻辑
    return { id: userId, name: 'John Doe' }
  },

  saveUserData: async (userData: any) => {
    // 保存用户数据的逻辑
    return { success: true }
  }
})
```

#### 事件管理

```typescript
// 广播事件到所有窗口
electronServer.broadcast('system-notification', {
  title: '系统通知',
  body: '这是一条系统通知'
})

// 发送事件到特定窗口
electronServer.emit(targetWindow, 'private-message', {
  message: '这是私有消息'
})

// 监听渲染进程事件
electronServer.onRendererEvent('error-occurred', (errorData) => {
  console.error('渲染进程错误:', errorData)
  // 可以在这里添加错误上报逻辑
})
```

## 🔧 类型定义

### 添加新的 API 方法

在 `src/method.types.d.ts` 中的 `MainProcessAPI` 接口添加新方法：

```typescript
export interface MainProcessAPI {
  // 现有方法...

  // 添加新方法
  getUserProfile: (userId: string) => Promise<UserProfile>
  updateUserProfile: (profile: UserProfile) => Promise<void>
}
```

### 添加新的事件类型

```typescript
export interface MainProcessEvents {
  // 现有事件...

  // 添加新事件
  'user-profile-updated': UserProfile
  'system-maintenance': MaintenanceInfo
}
```

## 🛠️ 最佳实践

### 1. 错误处理

```typescript
try {
  const result = await electronClient.invoke('someMethod', params)
  // 处理成功结果
} catch (error) {
  console.error('调用失败:', error.message)
  // 处理错误
}
```

### 2. 事件清理

```typescript
// 在组件卸载时清理事件监听器
onUnmounted(() => {
  electronClient.cleanup()
})
```

### 3. 类型安全

```typescript
// 使用类型断言确保类型安全
const settings = await electronClient.getSettings() as AppSettings
```

## 🔍 调试技巧

### 1. 启用详细日志

在主进程中：
```typescript
electronServer.on('api-call', (method, args) => {
  console.log(`API调用: ${method}`, args)
})
```

### 2. 事件追踪

```typescript
electronClient.on('*', (event, data) => {
  console.log(`事件: ${event}`, data)
})
```

## 🚨 注意事项

1. **安全性**: 确保只暴露必要的 API，避免暴露敏感操作
2. **性能**: 避免频繁的 IPC 调用，考虑批量操作
3. **错误处理**: 始终处理可能的错误情况
4. **类型检查**: 充分利用 TypeScript 的类型检查功能

## 📝 更新日志

- v1.0.0: 初始版本，包含基础的 IPC 架构和类型定义
