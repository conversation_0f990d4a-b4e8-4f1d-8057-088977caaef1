/**
 * 测试 ElectronClient 事件功能
 * 这个文件用于验证 ElectronClient 的事件监听功能是否正常工作
 */

import { electronClient } from './src/renderer/src/utils/electronClient'

// 测试本地事件功能
function testLocalEvents() {
	console.log('=== 测试本地事件功能 ===')
	
	// 监听本地事件
	electronClient.addEventListener('test-event', (data) => {
		console.log('收到本地事件:', data)
	})
	
	// 发送本地事件
	electronClient.emitLocal('test-event', { message: 'Hello from local event!' })
	
	// 一次性监听
	electronClient.onceLocal('once-event', (data) => {
		console.log('收到一次性事件:', data)
	})
	
	// 发送一次性事件
	electronClient.emitLocal('once-event', { message: 'This should only be received once' })
	electronClient.emitLocal('once-event', { message: 'This should not be received' })
}

// 测试主进程事件功能（兼容性）
function testMainProcessEvents() {
	console.log('=== 测试主进程事件功能（兼容性） ===')
	
	// 使用新的方法
	const unsubscribe = electronClient.onMainProcess('system-theme-changed', (theme) => {
		console.log('主题变化:', theme)
	})
	
	// 使用兼容的旧方法
	const unsubscribeOld = electronClient.on('notification', (notification) => {
		console.log('收到通知:', notification)
	})
	
	// 便捷方法测试
	const unsubscribeTheme = electronClient.onThemeChange((theme) => {
		console.log('便捷方法 - 主题变化:', theme)
	})
	
	// 清理监听器
	setTimeout(() => {
		unsubscribe()
		unsubscribeOld()
		unsubscribeTheme()
		console.log('已清理主进程事件监听器')
	}, 1000)
}

// 测试下载任务事件
function testDownloadTaskEvents() {
	console.log('=== 测试下载任务事件 ===')
	
	// 模拟下载任务
	const taskId = 'test-task-123'
	
	// 监听下载事件
	electronClient.addEventListener(`complete-${taskId}`, () => {
		console.log('下载完成事件')
	})
	
	electronClient.addEventListener(`progress-${taskId}`, (progress) => {
		console.log('下载进度事件:', progress)
	})
	
	electronClient.addEventListener(`error-${taskId}`, (error) => {
		console.log('下载错误事件:', error)
	})
	
	// 模拟发送下载事件
	setTimeout(() => {
		electronClient.emitLocal(`progress-${taskId}`, { percent: 50, speed: '1MB/s' })
		electronClient.emitLocal(`progress-${taskId}`, { percent: 100, speed: '1MB/s' })
		electronClient.emitLocal(`complete-${taskId}`)
	}, 500)
}

// 测试事件计数和管理
function testEventManagement() {
	console.log('=== 测试事件管理功能 ===')
	
	const eventName = 'management-test'
	
	// 添加多个监听器
	electronClient.addEventListener(eventName, () => console.log('监听器 1'))
	electronClient.addEventListener(eventName, () => console.log('监听器 2'))
	electronClient.addEventListener(eventName, () => console.log('监听器 3'))
	
	console.log(`事件 ${eventName} 的监听器数量:`, electronClient.listenerCount(eventName))
	console.log('所有事件名称:', electronClient.eventNames())
	
	// 发送事件
	electronClient.emitLocal(eventName, 'test data')
	
	// 清理所有事件
	setTimeout(() => {
		electronClient.cleanup()
		console.log('已清理所有事件监听器')
		console.log(`清理后事件 ${eventName} 的监听器数量:`, electronClient.listenerCount(eventName))
	}, 1000)
}

// 运行所有测试
function runAllTests() {
	console.log('开始测试 ElectronClient 事件功能...\n')
	
	testLocalEvents()
	setTimeout(() => testMainProcessEvents(), 100)
	setTimeout(() => testDownloadTaskEvents(), 200)
	setTimeout(() => testEventManagement(), 300)
	
	setTimeout(() => {
		console.log('\n所有测试完成!')
	}, 2000)
}

// 如果直接运行此文件，执行测试
if (typeof window !== 'undefined' && window.electronAPI) {
	runAllTests()
} else {
	console.log('此测试需要在 Electron 渲染进程中运行')
}

export {
	testLocalEvents,
	testMainProcessEvents,
	testDownloadTaskEvents,
	testEventManagement,
	runAllTests
}
