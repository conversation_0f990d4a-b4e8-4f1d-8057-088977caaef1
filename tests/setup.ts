/**
 * 测试环境设置文件
 * 在所有测试运行前执行的全局设置
 */

import { vi } from 'vitest'

// Mock Electron APIs
const mockElectron = {
	app: {
		whenReady: vi.fn().mockResolvedValue(undefined),
		on: vi.fn(),
		quit: vi.fn(),
		getPath: vi.fn().mockReturnValue('/mock/path'),
		getName: vi.fn().mockReturnValue('TestApp'),
		getVersion: vi.fn().mockReturnValue('1.0.0')
	},
	BrowserWindow: vi.fn().mockImplementation(() => ({
		loadURL: vi.fn(),
		loadFile: vi.fn(),
		on: vi.fn(),
		webContents: {
			setWindowOpenHandler: vi.fn(),
			send: vi.fn()
		},
		show: vi.fn(),
		hide: vi.fn(),
		close: vi.fn(),
		destroy: vi.fn()
	})),
	ipcMain: {
		on: vi.fn(),
		handle: vi.fn(),
		removeAllListeners: vi.fn()
	},
	ipcRenderer: {
		invoke: vi.fn(),
		on: vi.fn(),
		removeAllListeners: vi.fn(),
		send: vi.fn()
	},
	shell: {
		openExternal: vi.fn()
	},
	dialog: {
		showOpenDialog: vi.fn(),
		showSaveDialog: vi.fn(),
		showMessageBox: vi.fn()
	}
}

// Mock electron module
vi.mock('electron', () => mockElectron)

// Mock @electron-toolkit/utils
vi.mock('@electron-toolkit/utils', () => ({
	electronApp: {
		setAppUserModelId: vi.fn()
	},
	optimizer: {
		watchWindowShortcuts: vi.fn()
	},
	is: {
		dev: false,
		mac: false,
		windows: false,
		linux: false
	}
}))

// Mock Node.js path module for consistent behavior
vi.mock('path', async () => {
	const actual = await vi.importActual('path')
	return {
		...actual,
		join: vi.fn((...args) => args.join('/')),
		resolve: vi.fn((...args) => '/' + args.join('/'))
	}
})

// 全局测试工具函数
global.createMockBrowserWindow = () => {
	return new mockElectron.BrowserWindow()
}

// 清理函数
afterEach(() => {
	vi.clearAllMocks()
})
