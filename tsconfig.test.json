{"extends": "./tsconfig.json", "compilerOptions": {"composite": false, "types": ["vitest/globals", "jsdom", "node"], "moduleResolution": "bundler", "allowImportingTsExtensions": false, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "preserve", "lib": ["ES2020", "DOM", "DOM.Iterable"], "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "baseUrl": ".", "paths": {"@/*": ["src/*"], "@main/*": ["src/main/*"], "@preload/*": ["src/preload/*"], "@renderer/*": ["src/renderer/*"], "@core/*": ["src/core/*"], "@tests/*": ["tests/*"]}}, "include": ["src/**/*", "tests/**/*", "vitest.config.ts"], "exclude": ["node_modules", "out", "dist", ".electron-vite"]}