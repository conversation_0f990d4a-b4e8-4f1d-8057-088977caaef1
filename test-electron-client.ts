// 简单测试 ElectronClient 的继承功能
import { ElectronClient } from './src/renderer/src/utils/electronClient'

// 模拟 window.electronAPI
const mockElectronAPI = {
  invoke: jest.fn(),
  on: jest.fn(),
  once: jest.fn(),
  off: jest.fn(),
  emit: jest.fn()
}

// 设置全局 window 对象
global.window = {
  electronAPI: mockElectronAPI
} as any

describe('ElectronClient EventEmitter 继承测试', () => {
  let client: ElectronClient

  beforeEach(() => {
    client = new ElectronClient()
  })

  afterEach(() => {
    client.cleanup()
  })

  test('应该能够使用继承的 EventEmitter 方法', () => {
    const listener = jest.fn()
    
    // 测试 on 方法（继承自 EventEmitter）
    client.on('test-event', listener)
    
    // 测试 emit 方法（继承自 EventEmitter）
    client.emit('test-event', 'test-data')
    
    expect(listener).toHaveBeenCalledWith('test-data')
  })

  test('应该能够使用 addEventListener 方法', () => {
    const listener = jest.fn()
    
    // 测试 addEventListener 方法
    client.addEventListener('test-event', listener)
    
    // 使用继承的 emit 方法触发事件
    client.emit('test-event', 'test-data')
    
    expect(listener).toHaveBeenCalledWith('test-data')
  })

  test('应该能够使用 emitLocal 方法', () => {
    const listener = jest.fn()
    
    client.on('test-event', listener)
    
    // 测试 emitLocal 方法
    const result = client.emitLocal('test-event', 'test-data')
    
    expect(result).toBe(true)
    expect(listener).toHaveBeenCalledWith('test-data')
  })

  test('应该能够使用 removeEventListener 方法', () => {
    const listener = jest.fn()
    
    client.addEventListener('test-event', listener)
    client.removeEventListener('test-event', listener)
    
    client.emit('test-event', 'test-data')
    
    expect(listener).not.toHaveBeenCalled()
  })

  test('应该能够使用 onceLocal 方法', () => {
    const listener = jest.fn()
    
    client.onceLocal('test-event', listener)
    
    // 第一次触发
    client.emit('test-event', 'test-data-1')
    expect(listener).toHaveBeenCalledWith('test-data-1')
    
    // 第二次触发，应该不会被调用
    client.emit('test-event', 'test-data-2')
    expect(listener).toHaveBeenCalledTimes(1)
  })

  test('应该能够获取事件监听器数量', () => {
    const listener1 = jest.fn()
    const listener2 = jest.fn()
    
    client.on('test-event', listener1)
    client.on('test-event', listener2)
    
    expect(client.listenerCount('test-event')).toBe(2)
  })

  test('应该能够获取事件名称', () => {
    const listener = jest.fn()
    
    client.on('test-event-1', listener)
    client.on('test-event-2', listener)
    
    const eventNames = client.eventNames()
    expect(eventNames).toContain('test-event-1')
    expect(eventNames).toContain('test-event-2')
  })

  test('cleanup 方法应该清理所有监听器', () => {
    const listener = jest.fn()
    
    client.on('test-event', listener)
    expect(client.listenerCount('test-event')).toBe(1)
    
    client.cleanup()
    expect(client.listenerCount('test-event')).toBe(0)
  })
})
