// 简单的测试脚本来验证download-event事件是否能正常工作
// 这个脚本可以在渲染进程中运行来测试事件监听

console.log('Testing download-event functionality...')

// 模拟创建electronClient实例
if (window.electronAPI) {
    console.log('ElectronAPI is available')
    
    // 监听download-event事件
    const unsubscribe = window.electronAPI.on('download-event', (downloadEvent) => {
        console.log('Received download-event:', downloadEvent)
        console.log('Event type:', downloadEvent.event)
        console.log('Task ID:', downloadEvent.taskId)
        console.log('Data:', downloadEvent.data)
    })
    
    console.log('Download event listener registered')
    
    // 可以在控制台中手动触发下载任务来测试
    console.log('You can now test by calling:')
    console.log('window.electronAPI.invoke("startDownloadTask", { pack: "test", url: "https://example.com/test.zip", version: "1.0.0", taskId: "test-123" })')
    
} else {
    console.error('ElectronAPI is not available')
}
